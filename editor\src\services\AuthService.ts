/**
 * 认证服务
 * 负责用户认证和授权
 */
import axios from 'axios';
import { EventEmitter } from '../utils/EventEmitter';

// 用户接口
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  role: string;
}

// API 响应接口
interface AuthResponse {
  token: string;
  user: User;
}

// 认证状态接口
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
}

// 认证事件类型
export enum AuthEventType {
  LOGIN_SUCCESS = 'loginSuccess',
  LOGIN_ERROR = 'loginError',
  LOGOUT = 'logout',
  REGISTER_SUCCESS = 'registerSuccess',
  REGISTER_ERROR = 'registerError',
  AUTH_STATE_CHANGED = 'authStateChanged',
}

// 认证服务类
class AuthService extends EventEmitter {
  private static instance: AuthService;
  
  private user: User | null = null;
  private token: string | null = null;
  private isAuthenticated: boolean = false;
  
  private constructor() {
    super();

    // 从本地存储加载令牌（检查是否在浏览器环境中）
    if (typeof window !== 'undefined' && window.localStorage) {
      this.token = localStorage.getItem('token');

      // 如果有令牌，设置axios默认头
      if (this.token) {
        axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
      }
    }
  }
  
  /**
   * 获取认证服务实例
   */
  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }
  
  /**
   * 登录
   * @param email 邮箱
   * @param password 密码
   */
  public async login(email: string, password: string): Promise<User> {
    try {
      const response = await axios.post<AuthResponse>('/api/auth/login', { email, password });

      const { token, user } = response.data;

      // 保存令牌和用户信息
      this.token = token;
      this.user = user;
      this.isAuthenticated = true;

      // 保存令牌到本地存储
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem('token', token);
      }

      // 设置axios默认头
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

      // 发出登录成功事件
      this.emit(AuthEventType.LOGIN_SUCCESS, user);
      this.emit(AuthEventType.AUTH_STATE_CHANGED, { isAuthenticated: true, user } as AuthState);

      return user;
    } catch (error) {
      console.error('登录失败:', error);
      this.emit(AuthEventType.LOGIN_ERROR, error);
      throw error;
    }
  }
  
  /**
   * 注册
   * @param username 用户名
   * @param email 邮箱
   * @param password 密码
   */
  public async register(username: string, email: string, password: string): Promise<User> {
    try {
      const response = await axios.post<AuthResponse>('/api/auth/register', { username, email, password });

      const { token, user } = response.data;

      // 保存令牌和用户信息
      this.token = token;
      this.user = user;
      this.isAuthenticated = true;

      // 保存令牌到本地存储
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem('token', token);
      }

      // 设置axios默认头
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

      // 发出注册成功事件
      this.emit(AuthEventType.REGISTER_SUCCESS, user);
      this.emit(AuthEventType.AUTH_STATE_CHANGED, { isAuthenticated: true, user } as AuthState);

      return user;
    } catch (error) {
      console.error('注册失败:', error);
      this.emit(AuthEventType.REGISTER_ERROR, error);
      throw error;
    }
  }
  
  /**
   * 登出
   */
  public logout(): void {
    // 清除令牌和用户信息
    this.token = null;
    this.user = null;
    this.isAuthenticated = false;
    
    // 清除本地存储中的令牌
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.removeItem('token');
    }
    
    // 清除axios默认头
    delete axios.defaults.headers.common['Authorization'];
    
    // 发出登出事件
    this.emit(AuthEventType.LOGOUT);
    this.emit(AuthEventType.AUTH_STATE_CHANGED, { isAuthenticated: false, user: null } as AuthState);
  }
  
  /**
   * 检查认证状态
   */
  public async checkAuth(): Promise<User | null> {
    // 如果没有令牌，直接返回null
    if (!this.token) {
      this.isAuthenticated = false;
      this.user = null;
      this.emit(AuthEventType.AUTH_STATE_CHANGED, { isAuthenticated: false, user: null } as AuthState);
      return null;
    }

    try {
      // 获取当前用户信息
      const response = await axios.get<User>('/api/auth/me');

      this.user = response.data;
      this.isAuthenticated = true;

      this.emit(AuthEventType.AUTH_STATE_CHANGED, { isAuthenticated: true, user: this.user } as AuthState);

      return this.user;
    } catch (error) {
      console.error('检查认证状态失败:', error);

      // 清除令牌和用户信息
      this.token = null;
      this.user = null;
      this.isAuthenticated = false;

      // 清除本地存储中的令牌
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.removeItem('token');
      }

      // 清除axios默认头
      delete axios.defaults.headers.common['Authorization'];

      this.emit(AuthEventType.AUTH_STATE_CHANGED, { isAuthenticated: false, user: null } as AuthState);

      return null;
    }
  }
  
  /**
   * 更新用户信息
   * @param data 用户数据
   */
  public async updateProfile(data: Partial<User>): Promise<User> {
    if (!this.isAuthenticated || !this.user) {
      throw new Error('未认证');
    }

    try {
      const response = await axios.patch<User>('/api/auth/profile', data);

      this.user = response.data;

      this.emit(AuthEventType.AUTH_STATE_CHANGED, { isAuthenticated: true, user: this.user } as AuthState);

      return this.user;
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  }
  
  /**
   * 更改密码
   * @param currentPassword 当前密码
   * @param newPassword 新密码
   */
  public async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    if (!this.isAuthenticated) {
      throw new Error('未认证');
    }
    
    try {
      await axios.post('/api/auth/change-password', { currentPassword, newPassword });
    } catch (error) {
      console.error('更改密码失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取当前用户
   */
  public getUser(): User | null {
    return this.user;
  }
  
  /**
   * 获取认证令牌
   */
  public getToken(): string | null {
    return this.token;
  }
  
  /**
   * 检查是否已认证
   */
  public isAuthenticatedUser(): boolean {
    return this.isAuthenticated;
  }
  
  /**
   * 销毁认证服务
   */
  public dispose(): void {
    this.removeAllListeners();
  }
}

export default AuthService.getInstance();
