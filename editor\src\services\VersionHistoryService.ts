/**
 * 版本历史服务
 * 负责管理编辑历史和版本回滚
 */
import { EventEmitter } from '../utils/EventEmitter';
import { message } from 'antd';
import { store } from '../store';
import {
  addVersion,
  setVersions,
  setCurrentVersion
} from '../store/collaboration/versionSlice';
import {
  Operation,
  OperationType,
  collaborationService
} from './CollaborationService';

// 版本接口
export interface Version {
  id: string;
  timestamp: number;
  operations: Operation[];
  snapshot?: any;
  description: string;
  userId: string;
  userName: string;
  tags?: string[];
}

/**
 * 版本历史服务类
 */
class VersionHistoryService extends EventEmitter {
  private versions: Version[] = [];
  private currentVersionId: string | null = null;
  private maxVersions: number = 100;
  private autoSaveInterval: number | null = null;
  private autoSaveEnabled: boolean = false;
  private autoSaveIntervalTime: number = 5 * 60 * 1000; // 5分钟
  private operationBuffer: Operation[] = [];
  private operationBufferSize: number = 20;
  private lastAutoSaveTime: number = 0;

  constructor() {
    super();

    // 监听协作服务的操作事件
    collaborationService.on('operation', this.handleOperation.bind(this));
  }

  /**
   * 初始化版本历史服务
   */
  public initialize(): void {
    // 加载设置
    this.loadSettings();

    // 加载历史版本
    this.loadVersions();

    // 启动自动保存
    if (this.autoSaveEnabled) {
      this.startAutoSave();
    }

    // 创建初始版本（如果没有版本）
    if (this.versions.length === 0) {
      this.createVersion('初始版本', ['initial']);
    }
  }

  /**
   * 加载设置
   */
  private loadSettings(): void {
    try {
      const settingsJson = localStorage.getItem('versionHistorySettings');

      if (settingsJson) {
        const settings = JSON.parse(settingsJson);

        this.autoSaveEnabled = settings.autoSaveEnabled ?? false;
        this.autoSaveIntervalTime = settings.autoSaveIntervalTime ?? 5 * 60 * 1000;
        this.maxVersions = settings.maxVersions ?? 100;
      }
    } catch (error) {
      console.error('加载版本历史设置失败:', error);
    }
  }

  /**
   * 保存设置到本地存储
   */
  private saveSettings(): void {
    try {
      const settings = {
        autoSaveEnabled: this.autoSaveEnabled,
        autoSaveIntervalTime: this.autoSaveIntervalTime,
        maxVersions: this.maxVersions
      };

      localStorage.setItem('versionHistorySettings', JSON.stringify(settings));
    } catch (error) {
      console.error('保存版本历史设置失败:', error);
    }
  }

  /**
   * 比较两个版本
   * @param versionId1 版本1 ID
   * @param versionId2 版本2 ID
   * @returns 差异对象
   */
  public compareVersions(versionId1: string, versionId2: string): any {
    const version1 = this.versions.find(v => v.id === versionId1);
    const version2 = this.versions.find(v => v.id === versionId2);

    if (!version1 || !version2) {
      console.error('找不到要比较的版本');
      return null;
    }

    // 如果有快照，比较快照
    if (version1.snapshot && version2.snapshot) {
      return this.compareSnapshots(version1.snapshot, version2.snapshot);
    }

    // 否则比较操作
    return this.compareOperations(version1.operations, version2.operations);
  }

  /**
   * 比较两个快照
   * @param snapshot1 快照1
   * @param snapshot2 快照2
   * @returns 差异对象
   */
  private compareSnapshots(snapshot1: any, snapshot2: any): any {
    // 创建差异对象
    const diff: any = {
      added: [],
      removed: [],
      modified: [],
      skybox: null,
      ambientLight: null,
      fog: null
    };

    // 比较实体
    const entities1 = snapshot1.entities || {};
    const entities2 = snapshot2.entities || {};

    // 查找添加的实体
    for (const id in entities2) {
      if (!(id in entities1)) {
        diff.added.push({
          type: 'entity',
          id,
          data: entities2[id]
        });
      }
    }

    // 查找删除的实体
    for (const id in entities1) {
      if (!(id in entities2)) {
        diff.removed.push({
          type: 'entity',
          id,
          data: entities1[id]
        });
      }
    }

    // 查找修改的实体
    for (const id in entities1) {
      if (id in entities2) {
        const entity1 = entities1[id];
        const entity2 = entities2[id];

        // 比较实体属性
        const entityDiff = this.compareObjects(entity1, entity2);
        if (entityDiff.hasChanges) {
          diff.modified.push({
            type: 'entity',
            id,
            data: {
              ...entity2,
              name: entity2.name || entity1.name
            },
            changes: entityDiff.changes
          });
        }
      }
    }

    // 比较天空盒
    if (snapshot1.skybox || snapshot2.skybox) {
      diff.skybox = {
        version1: snapshot1.skybox || null,
        version2: snapshot2.skybox || null,
        hasDifferences: JSON.stringify(snapshot1.skybox) !== JSON.stringify(snapshot2.skybox)
      };
    }

    // 比较环境光
    if (snapshot1.ambientLight || snapshot2.ambientLight) {
      diff.ambientLight = {
        version1: snapshot1.ambientLight || null,
        version2: snapshot2.ambientLight || null,
        hasDifferences: JSON.stringify(snapshot1.ambientLight) !== JSON.stringify(snapshot2.ambientLight)
      };
    }

    // 比较雾效
    if (snapshot1.fog || snapshot2.fog) {
      diff.fog = {
        version1: snapshot1.fog || null,
        version2: snapshot2.fog || null,
        hasDifferences: JSON.stringify(snapshot1.fog) !== JSON.stringify(snapshot2.fog)
      };
    }

    return diff;
  }

  /**
   * 比较两个对象
   * @param obj1 对象1
   * @param obj2 对象2
   * @returns 差异对象
   */
  private compareObjects(obj1: any, obj2: any): any {
    const result = {
      hasChanges: false,
      changes: {
        added: {},
        removed: {},
        modified: {}
      }
    };

    // 查找添加和修改的属性
    for (const key in obj2) {
      if (!(key in obj1)) {
        result.hasChanges = true;
        result.changes.added[key] = obj2[key];
      } else if (JSON.stringify(obj1[key]) !== JSON.stringify(obj2[key])) {
        result.hasChanges = true;
        result.changes.modified[key] = {
          from: obj1[key],
          to: obj2[key]
        };
      }
    }

    // 查找删除的属性
    for (const key in obj1) {
      if (!(key in obj2)) {
        result.hasChanges = true;
        result.changes.removed[key] = obj1[key];
      }
    }

    return result;
  }

  /**
   * 比较两组操作
   * @param operations1 操作组1
   * @param operations2 操作组2
   * @returns 差异对象
   */
  private compareOperations(operations1: Operation[], operations2: Operation[]): any {
    // 简单实现，实际应用中需要更复杂的比较算法
    const diff: any = {
      operations1: operations1.length,
      operations2: operations2.length,
      commonEntities: new Set<string>(),
      uniqueToOps1: new Set<string>(),
      uniqueToOps2: new Set<string>()
    };

    // 提取操作中涉及的实体ID
    const entityIds1 = new Set<string>();
    const entityIds2 = new Set<string>();

    operations1.forEach(op => {
      if (op.data?.entityId) {
        entityIds1.add(op.data.entityId);
      }
    });

    operations2.forEach(op => {
      if (op.data?.entityId) {
        entityIds2.add(op.data.entityId);
      }
    });

    // 找出共同的实体和唯一的实体
    for (const id of entityIds1) {
      if (entityIds2.has(id)) {
        diff.commonEntities.add(id);
      } else {
        diff.uniqueToOps1.add(id);
      }
    }

    for (const id of entityIds2) {
      if (!entityIds1.has(id)) {
        diff.uniqueToOps2.add(id);
      }
    }

    // 转换为数组以便序列化
    diff.commonEntities = Array.from(diff.commonEntities);
    diff.uniqueToOps1 = Array.from(diff.uniqueToOps1);
    diff.uniqueToOps2 = Array.from(diff.uniqueToOps2);

    return diff;
  }

  /**
   * 设置自动保存
   * @param enabled 是否启用
   * @param intervalTime 间隔时间（毫秒）
   */
  public setAutoSave(enabled: boolean, intervalTime?: number): void {
    this.autoSaveEnabled = enabled;

    if (intervalTime !== undefined) {
      this.autoSaveIntervalTime = intervalTime;
    }

    // 停止当前的自动保存
    this.stopAutoSave();

    // 如果启用，重新开始自动保存
    if (enabled) {
      this.startAutoSave();
    }

    // 保存设置到本地存储
    this.saveSettings();
  }

  /**
   * 设置是否启用自动保存
   * @param enabled 是否启用
   */
  public setAutoSaveEnabled(enabled: boolean): void {
    this.setAutoSave(enabled);
  }

  /**
   * 设置自动保存间隔时间
   * @param intervalMs 间隔时间（毫秒）
   */
  public setAutoSaveInterval(intervalMs: number): void {
    this.setAutoSave(this.autoSaveEnabled, intervalMs);
  }

  /**
   * 启动自动保存
   */
  private startAutoSave(): void {
    if (this.autoSaveInterval !== null) {
      return;
    }

    this.lastAutoSaveTime = Date.now();

    this.autoSaveInterval = window.setInterval(() => {
      const now = Date.now();

      // 只有当操作缓冲区不为空且距离上次自动保存超过间隔时间才创建自动保存版本
      if (this.operationBuffer.length > 0 && now - this.lastAutoSaveTime >= this.autoSaveIntervalTime) {
        this.createVersion('自动保存', ['auto']);
        this.lastAutoSaveTime = now;
      }
    }, Math.min(60000, this.autoSaveIntervalTime)) as unknown as number; // 最多每分钟检查一次
  }

  /**
   * 获取是否启用自动保存
   * @returns 是否启用自动保存
   */
  public isAutoSaveEnabled(): boolean {
    return this.autoSaveEnabled;
  }

  /**
   * 获取自动保存间隔时间（毫秒）
   * @returns 间隔时间
   */
  public getAutoSaveInterval(): number {
    return this.autoSaveIntervalTime;
  }

  /**
   * 停止自动保存
   */
  private stopAutoSave(): void {
    if (this.autoSaveInterval !== null) {
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
    }
  }

  /**
   * 处理操作
   * @param operation 操作
   */
  private handleOperation(operation: Operation): void {
    // 添加到操作缓冲区
    this.operationBuffer.push(operation);

    // 限制缓冲区大小
    if (this.operationBuffer.length > this.operationBufferSize) {
      this.operationBuffer.shift();
    }

    // 如果是重要操作，创建版本
    if (this.isSignificantOperation(operation)) {
      this.createVersion(this.getOperationDescription(operation));
    }
  }

  /**
   * 判断是否是重要操作
   * @param operation 操作
   * @returns 是否是重要操作
   */
  private isSignificantOperation(operation: Operation): boolean {
    // 实现重要操作判断逻辑
    // 例如，实体创建、删除等操作可能被视为重要操作
    switch (operation.type) {
      case OperationType.ENTITY_CREATE:
      case OperationType.ENTITY_DELETE:
      case OperationType.SCENE_UPDATE:
        return true;
      default:
        return false;
    }
  }

  /**
   * 获取操作描述
   * @param operation 操作
   * @returns 操作描述
   */
  private getOperationDescription(operation: Operation): string {
    switch (operation.type) {
      case OperationType.ENTITY_CREATE:
        return `创建实体 "${operation.data.name || operation.data.id}"`;
      case OperationType.ENTITY_UPDATE:
        return `更新实体 "${operation.data.name || operation.data.id}"`;
      case OperationType.ENTITY_DELETE:
        return `删除实体 "${operation.data.name || operation.data.id}"`;
      case OperationType.COMPONENT_ADD:
        return `添加组件 "${operation.data.componentType}"`;
      case OperationType.COMPONENT_UPDATE:
        return `更新组件 "${operation.data.componentType}"`;
      case OperationType.COMPONENT_REMOVE:
        return `移除组件 "${operation.data.componentType}"`;
      case OperationType.SCENE_UPDATE:
        return `更新场景`;
      default:
        return `未知操作`;
    }
  }

  /**
   * 创建版本
   * @param description 版本描述
   * @param tags 版本标签
   * @returns 版本ID
   */
  public createVersion(description: string, tags?: string[]): string {
    // 获取当前场景快照
    const snapshot = this.getSceneSnapshot();

    // 创建版本对象
    const version: Version = {
      id: this.generateId(),
      timestamp: Date.now(),
      operations: [...this.operationBuffer],
      snapshot,
      description,
      userId: this.getUserId(),
      userName: this.getUserName(),
      tags
    };

    // 添加到版本列表
    this.versions.push(version);

    // 限制版本数量
    if (this.versions.length > this.maxVersions) {
      this.versions.shift();
    }

    // 设置为当前版本
    this.currentVersionId = version.id;

    // 更新Redux状态
    store.dispatch(addVersion(version));
    store.dispatch(setCurrentVersion(version.id));

    // 保存版本
    this.saveVersions();

    // 发出版本创建事件
    this.emit('versionCreated', version);

    return version.id;
  }

  /**
   * 回滚到指定版本
   * @param versionId 版本ID
   * @param options 回滚选项
   * @returns 是否成功
   */
  public rollbackToVersion(versionId: string, options?: {
    entities?: boolean | string[];
    skybox?: boolean;
    ambientLight?: boolean;
    fog?: boolean;
  }): boolean {
    // 查找版本
    const version = this.versions.find(v => v.id === versionId);

    if (!version) {
      console.error(`找不到版本: ${versionId}`);
      return false;
    }

    // 默认选项
    const defaultOptions = {
      entities: true,
      skybox: true,
      ambientLight: true,
      fog: true
    };

    // 合并选项
    const mergedOptions = { ...defaultOptions, ...options };

    // 应用版本快照
    if (version.snapshot) {
      this.applySnapshot(version.snapshot, mergedOptions);
    } else {
      // 如果没有快照，尝试重放操作
      this.replayOperations(version, mergedOptions);
    }

    // 设置为当前版本
    this.currentVersionId = version.id;

    // 更新Redux状态
    store.dispatch(setCurrentVersion(version.id));

    // 发出版本回滚事件
    this.emit('versionRollback', version);

    // 通知用户
    message.success(`已回滚到版本: ${version.description}`);

    return true;
  }

  /**
   * 应用场景快照
   * @param snapshot 场景快照
   * @param options 应用选项
   */
  private applySnapshot(snapshot: any, options?: {
    entities?: boolean | string[];
    skybox?: boolean;
    ambientLight?: boolean;
    fog?: boolean;
  }): void {
    if (!snapshot) {
      console.error('无效的场景快照');
      return;
    }

    try {
      // 获取场景管理器
      const sceneManager = window.editor?.sceneManager;
      if (!sceneManager) {
        console.error('无法获取场景管理器');
        return;
      }

      // 默认选项
      const defaultOptions = {
        entities: true,
        skybox: true,
        ambientLight: true,
        fog: true
      };

      // 合并选项
      const mergedOptions = { ...defaultOptions, ...options };

      // 获取当前场景状态（如果需要部分回滚）
      const needsPartialRollback =
        !mergedOptions.entities ||
        (Array.isArray(mergedOptions.entities) && mergedOptions.entities.length > 0) ||
        !mergedOptions.skybox ||
        !mergedOptions.ambientLight ||
        !mergedOptions.fog;

      let currentSceneData = null;
      if (needsPartialRollback) {
        currentSceneData = sceneManager.exportSceneToJSON();
      }

      // 如果是完全回滚，清除当前场景并加载快照
      if (!needsPartialRollback) {
        sceneManager.clearScene();
        sceneManager.loadSceneFromJSON(snapshot);
      } else {
        // 部分回滚
        // 处理实体
        if (mergedOptions.entities) {
          // 如果是数组，只回滚特定实体
          if (Array.isArray(mergedOptions.entities)) {
            const selectedEntityIds = mergedOptions.entities;

            // 对于每个选定的实体ID
            for (const entityId of selectedEntityIds) {
              // 如果实体存在于快照中
              if (snapshot.entities && snapshot.entities[entityId]) {
                // 如果实体已存在，更新它
                if (sceneManager.entityExists(entityId)) {
                  sceneManager.updateEntity(entityId, snapshot.entities[entityId]);
                } else {
                  // 否则创建新实体
                  sceneManager.createEntity(entityId, snapshot.entities[entityId].type, snapshot.entities[entityId]);
                }
              } else if (currentSceneData?.entities && currentSceneData.entities[entityId]) {
                // 如果实体在当前场景中存在但在快照中不存在，删除它
                sceneManager.removeEntity(entityId);
              }
            }
          } else {
            // 回滚所有实体
            // 先清除所有实体
            sceneManager.clearEntities();

            // 加载快照中的实体
            if (snapshot.entities) {
              for (const entityId in snapshot.entities) {
                const entityData = snapshot.entities[entityId];
                sceneManager.createEntity(entityId, entityData.type, entityData);
              }
            }
          }
        }

        // 处理天空盒
        if (mergedOptions.skybox && snapshot.skybox) {
          sceneManager.setSkybox(snapshot.skybox);
        }

        // 处理环境光
        if (mergedOptions.ambientLight && snapshot.ambientLight) {
          sceneManager.setAmbientLight(snapshot.ambientLight);
        }

        // 处理雾效
        if (mergedOptions.fog && snapshot.fog) {
          sceneManager.setFog(snapshot.fog);
        }
      }

      // 通知场景已更新
      sceneManager.notifySceneChanged();

      console.log('已应用场景快照', mergedOptions);
    } catch (error) {
      console.error('应用场景快照时出错:', error);
    }
  }

  /**
   * 重放操作
   * @param version 版本
   * @param options 回滚选项
   */
  private replayOperations(version: Version, options?: {
    entities?: boolean | string[];
    skybox?: boolean;
    ambientLight?: boolean;
    fog?: boolean;
  }): void {
    if (!version.operations || version.operations.length === 0) {
      console.warn('没有操作可重放');
      return;
    }

    try {
      // 获取场景管理器
      const sceneManager = window.editor?.sceneManager;
      if (!sceneManager) {
        console.error('无法获取场景管理器');
        return;
      }

      // 默认选项
      const defaultOptions = {
        entities: true,
        skybox: true,
        ambientLight: true,
        fog: true
      };

      // 合并选项
      const mergedOptions = { ...defaultOptions, ...options };

      // 获取当前场景状态（如果需要部分回滚）
      const needsPartialRollback =
        !mergedOptions.entities ||
        (Array.isArray(mergedOptions.entities) && mergedOptions.entities.length > 0) ||
        !mergedOptions.skybox ||
        !mergedOptions.ambientLight ||
        !mergedOptions.fog;

      // 如果是完全回滚
      if (!needsPartialRollback) {
        // 清除当前场景
        sceneManager.clearScene();

        // 创建一个新的空场景
        sceneManager.createNewScene();

        // 按时间顺序排序操作
        const sortedOperations = [...version.operations].sort((a, b) => a.timestamp - b.timestamp);

        // 重放每个操作
        for (const operation of sortedOperations) {
          this.applyOperation(operation);
        }
      } else {
        // 部分回滚
        // 按时间顺序排序操作
        const sortedOperations = [...version.operations].sort((a, b) => a.timestamp - b.timestamp);

        // 过滤操作
        const filteredOperations = sortedOperations.filter(operation => {
          // 实体相关操作
          if (operation.type === OperationType.ENTITY_CREATE ||
              operation.type === OperationType.ENTITY_UPDATE ||
              operation.type === OperationType.ENTITY_DELETE ||
              operation.type === OperationType.COMPONENT_ADD ||
              operation.type === OperationType.COMPONENT_UPDATE ||
              operation.type === OperationType.COMPONENT_REMOVE ||
              operation.type === OperationType.PROPERTY_UPDATE) {

            // 如果不回滚实体，跳过所有实体相关操作
            if (!mergedOptions.entities) {
              return false;
            }

            // 如果只回滚特定实体
            if (Array.isArray(mergedOptions.entities)) {
              const entityId = operation.data?.entityId;
              return entityId && mergedOptions.entities.includes(entityId);
            }

            // 回滚所有实体
            return true;
          }

          // 场景更新操作
          if (operation.type === OperationType.SCENE_UPDATE) {
            const updateType = operation.data?.updateType;

            // 根据更新类型过滤
            if (updateType === 'skybox') {
              return mergedOptions.skybox;
            } else if (updateType === 'ambientLight') {
              return mergedOptions.ambientLight;
            } else if (updateType === 'fog') {
              return mergedOptions.fog;
            }
          }

          // 默认不过滤
          return true;
        });

        // 如果需要回滚所有实体，先清除现有实体
        if (mergedOptions.entities === true) {
          sceneManager.clearEntities();
        }

        // 重放过滤后的操作
        for (const operation of filteredOperations) {
          this.applyOperation(operation);
        }
      }

      // 通知场景已更新
      sceneManager.notifySceneChanged();

      console.log('已重放操作:', needsPartialRollback ? '部分回滚' : '完全回滚');
    } catch (error) {
      console.error('重放操作时出错:', error);
    }
  }

  /**
   * 应用单个操作
   * @param operation 操作
   */
  private applyOperation(operation: Operation): void {
    try {
      // 根据操作类型应用操作
      switch (operation.type) {
        case OperationType.ENTITY_CREATE:
          this.applyEntityCreateOperation(operation);
          break;

        case OperationType.ENTITY_UPDATE:
          this.applyEntityUpdateOperation(operation);
          break;

        case OperationType.ENTITY_DELETE:
          this.applyEntityDeleteOperation(operation);
          break;

        case OperationType.COMPONENT_ADD:
          this.applyComponentAddOperation(operation);
          break;

        case OperationType.COMPONENT_UPDATE:
          this.applyComponentUpdateOperation(operation);
          break;

        case OperationType.COMPONENT_REMOVE:
          this.applyComponentRemoveOperation(operation);
          break;

        case OperationType.SCENE_UPDATE:
          this.applySceneUpdateOperation(operation);
          break;

        case OperationType.PROPERTY_UPDATE:
          this.applyPropertyUpdateOperation(operation);
          break;

        default:
          console.warn('未知的操作类型:', operation.type);
          break;
      }
    } catch (error) {
      console.error('应用操作时出错:', error, operation);
    }
  }

  /**
   * 应用实体创建操作
   */
  private applyEntityCreateOperation(operation: Operation): void {
    const sceneManager = window.editor?.sceneManager;
    if (!sceneManager) return;

    const { entityId, entityType, properties } = operation.data || {};
    if (!entityId || !entityType) return;

    // 创建实体
    sceneManager.createEntity(entityId, entityType, properties);
  }

  /**
   * 应用实体更新操作
   */
  private applyEntityUpdateOperation(operation: Operation): void {
    const sceneManager = window.editor?.sceneManager;
    if (!sceneManager) return;

    const { entityId, properties } = operation.data || {};
    if (!entityId || !properties) return;

    // 更新实体
    sceneManager.updateEntity(entityId, properties);
  }

  /**
   * 应用实体删除操作
   */
  private applyEntityDeleteOperation(operation: Operation): void {
    const sceneManager = window.editor?.sceneManager;
    if (!sceneManager) return;

    const { entityId } = operation.data || {};
    if (!entityId) return;

    // 删除实体
    sceneManager.deleteEntity(entityId);
  }

  /**
   * 应用组件添加操作
   */
  private applyComponentAddOperation(operation: Operation): void {
    const sceneManager = window.editor?.sceneManager;
    if (!sceneManager) return;

    const { entityId, componentId, componentType, properties } = operation.data || {};
    if (!entityId || !componentId || !componentType) return;

    // 添加组件
    sceneManager.addComponent(entityId, componentId, componentType, properties);
  }

  /**
   * 应用组件更新操作
   */
  private applyComponentUpdateOperation(operation: Operation): void {
    const sceneManager = window.editor?.sceneManager;
    if (!sceneManager) return;

    const { entityId, componentId, properties } = operation.data || {};
    if (!entityId || !componentId || !properties) return;

    // 更新组件
    sceneManager.updateComponent(entityId, componentId, properties);
  }

  /**
   * 应用组件移除操作
   */
  private applyComponentRemoveOperation(operation: Operation): void {
    const sceneManager = window.editor?.sceneManager;
    if (!sceneManager) return;

    const { entityId, componentId } = operation.data || {};
    if (!entityId || !componentId) return;

    // 移除组件
    sceneManager.removeComponent(entityId, componentId);
  }

  /**
   * 应用场景更新操作
   */
  private applySceneUpdateOperation(operation: Operation): void {
    const sceneManager = window.editor?.sceneManager;
    if (!sceneManager) return;

    const { properties } = operation.data || {};
    if (!properties) return;

    // 更新场景
    sceneManager.updateScene(properties);
  }

  /**
   * 应用属性更新操作
   */
  private applyPropertyUpdateOperation(operation: Operation): void {
    const sceneManager = window.editor?.sceneManager;
    if (!sceneManager) return;

    const { entityId, path, value } = operation.data || {};
    if (!entityId || !path || value === undefined) return;

    // 更新属性
    sceneManager.updateProperty(entityId, path, value);
  }

  /**
   * 获取场景快照
   * @returns 场景快照
   */
  private getSceneSnapshot(): any {
    try {
      // 获取场景管理器
      const sceneManager = window.editor?.sceneManager;
      if (!sceneManager) {
        console.error('无法获取场景管理器');
        return null;
      }

      // 获取场景JSON
      const sceneJSON = sceneManager.exportSceneToJSON();

      return sceneJSON;
    } catch (error) {
      console.error('获取场景快照时出错:', error);
      return null;
    }
  }

  /**
   * 加载版本历史
   */
  private loadVersions(): void {
    try {
      const versionsJson = localStorage.getItem('versionHistory');

      if (versionsJson) {
        this.versions = JSON.parse(versionsJson);
        store.dispatch(setVersions(this.versions));
      }
    } catch (error) {
      console.error('加载版本历史失败:', error);
    }
  }

  /**
   * 保存版本历史
   */
  private saveVersions(): void {
    try {
      localStorage.setItem('versionHistory', JSON.stringify(this.versions));
    } catch (error) {
      console.error('保存版本历史失败:', error);
    }
  }

  /**
   * 获取当前用户ID
   */
  private getUserId(): string {
    return localStorage.getItem('userId') || 'anonymous';
  }

  /**
   * 获取当前用户名
   */
  private getUserName(): string {
    return localStorage.getItem('userName') || 'Anonymous';
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
  }

  /**
   * 获取所有版本
   */
  public getAllVersions(): Version[] {
    return [...this.versions];
  }

  /**
   * 获取当前版本
   */
  public getCurrentVersion(): Version | null {
    if (!this.currentVersionId) {
      return null;
    }

    return this.versions.find(v => v.id === this.currentVersionId) || null;
  }

  /**
   * 合并两个版本
   * @param sourceVersionId 源版本ID
   * @param targetVersionId 目标版本ID
   * @param options 合并选项
   * @returns 新版本ID
   */
  public mergeVersions(
    sourceVersionId: string,
    targetVersionId: string,
    options?: {
      entities?: 'version1' | 'version2' | 'both' | string[];
      skybox?: 'version1' | 'version2' | 'none';
      ambientLight?: 'version1' | 'version2' | 'none';
      fog?: 'version1' | 'version2' | 'none';
    }
  ): string | null {
    // 查找版本
    const sourceVersion = this.versions.find(v => v.id === sourceVersionId);
    const targetVersion = this.versions.find(v => v.id === targetVersionId);

    if (!sourceVersion || !targetVersion) {
      console.error('找不到要合并的版本');
      return null;
    }

    // 确保两个版本都有快照
    if (!sourceVersion.snapshot || !targetVersion.snapshot) {
      console.error('版本缺少快照数据，无法合并');
      return null;
    }

    // 默认选项
    const defaultOptions = {
      entities: 'both',
      skybox: 'version2',
      ambientLight: 'version2',
      fog: 'version2'
    };

    // 合并选项
    const mergedOptions = { ...defaultOptions, ...options };

    try {
      // 创建合并后的快照
      const mergedSnapshot: any = {
        entities: {},
        skybox: null,
        ambientLight: null,
        fog: null
      };

      // 处理实体
      if (mergedOptions.entities === 'version1') {
        // 使用源版本的实体
        mergedSnapshot.entities = { ...sourceVersion.snapshot.entities };
      } else if (mergedOptions.entities === 'version2') {
        // 使用目标版本的实体
        mergedSnapshot.entities = { ...targetVersion.snapshot.entities };
      } else if (Array.isArray(mergedOptions.entities)) {
        // 使用选定的实体
        const selectedEntityIds = mergedOptions.entities;

        // 首先复制源版本的所有实体
        mergedSnapshot.entities = { ...sourceVersion.snapshot.entities };

        // 然后对于选定的实体，使用目标版本的数据
        for (const entityId of selectedEntityIds) {
          if (targetVersion.snapshot.entities && targetVersion.snapshot.entities[entityId]) {
            mergedSnapshot.entities[entityId] = targetVersion.snapshot.entities[entityId];
          }
        }
      } else {
        // 合并两个版本的实体
        // 首先复制源版本的所有实体
        mergedSnapshot.entities = { ...sourceVersion.snapshot.entities };

        // 然后添加或覆盖目标版本的实体
        if (targetVersion.snapshot.entities) {
          for (const entityId in targetVersion.snapshot.entities) {
            mergedSnapshot.entities[entityId] = targetVersion.snapshot.entities[entityId];
          }
        }
      }

      // 处理天空盒
      if (mergedOptions.skybox === 'version1') {
        mergedSnapshot.skybox = sourceVersion.snapshot.skybox;
      } else if (mergedOptions.skybox === 'version2') {
        mergedSnapshot.skybox = targetVersion.snapshot.skybox;
      }

      // 处理环境光
      if (mergedOptions.ambientLight === 'version1') {
        mergedSnapshot.ambientLight = sourceVersion.snapshot.ambientLight;
      } else if (mergedOptions.ambientLight === 'version2') {
        mergedSnapshot.ambientLight = targetVersion.snapshot.ambientLight;
      }

      // 处理雾效
      if (mergedOptions.fog === 'version1') {
        mergedSnapshot.fog = sourceVersion.snapshot.fog;
      } else if (mergedOptions.fog === 'version2') {
        mergedSnapshot.fog = targetVersion.snapshot.fog;
      }

      // 创建新版本
      const newVersionId = this.createVersion(
        `合并版本: ${sourceVersion.description} + ${targetVersion.description}`,
        ['merged']
      );

      // 获取新创建的版本
      const newVersion = this.versions.find(v => v.id === newVersionId);
      if (newVersion) {
        // 更新快照
        newVersion.snapshot = mergedSnapshot;

        // 保存版本
        this.saveVersions();

        // 发出版本合并事件
        this.emit('versionMerged', {
          sourceVersion,
          targetVersion,
          newVersion,
          options: mergedOptions
        });

        return newVersionId;
      }

      return null;
    } catch (error) {
      console.error('合并版本时出错:', error);
      return null;
    }
  }
}

// 创建单例实例
export const versionHistoryService = new VersionHistoryService();
