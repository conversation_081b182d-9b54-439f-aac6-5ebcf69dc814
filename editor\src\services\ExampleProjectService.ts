/**
 * 示例项目服务
 * 负责管理编辑器的示例项目
 */
import { EventEmitter } from '../utils/EventEmitter';
import { exampleProjects as defaultExamples } from '../data/exampleProjects';

/**
 * 示例项目接口
 */
export interface ExampleProject {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  thumbnailUrl: string;
  path: string;
  tags?: string[];
  features?: { title: string; description: string }[];
  prerequisites?: string[];
  estimatedTime?: number; // 预计完成时间（分钟）
  author?: string;
  version?: string;
  lastUpdated?: string;
  popularity?: number; // 受欢迎程度（0-100）
  rating?: number; // 评分（0-5）
  tutorialUrl?: string; // 相关教程URL
  videoTutorialId?: string; // 相关视频教程ID
  interactiveTutorialId?: string; // 相关交互式教程ID
}

/**
 * 示例项目服务类
 */
export class ExampleProjectService {
  private static instance: ExampleProjectService;
  private examples: ExampleProject[] = [];
  private events = new EventEmitter();
  private viewedExamples: Set<string> = new Set();
  private favoriteExamples: Set<string> = new Set();
  private recentlyViewedExamples: string[] = [];

  private constructor() {
    this.loadExampleProjects();
    this.loadUserData();
  }

  /**
   * 获取示例项目服务实例
   */
  public static getInstance(): ExampleProjectService {
    if (!ExampleProjectService.instance) {
      ExampleProjectService.instance = new ExampleProjectService();
    }
    return ExampleProjectService.instance;
  }

  /**
   * 加载示例项目
   */
  private loadExampleProjects(): void {
    // 从默认数据加载示例项目
    this.examples = [...defaultExamples];

    // 从服务器加载更多示例项目
    this.fetchExampleProjects()
      .then(examples => {
        // 合并示例项目，避免重复
        const existingIds = new Set(this.examples.map(example => example.id));
        const newExamples = examples.filter(example => !existingIds.has(example.id));
        this.examples = [...this.examples, ...newExamples];
        this.events.emit('examplesLoaded', this.examples);
      })
      .catch(error => {
        console.error('Failed to fetch example projects:', error);
      });
  }

  /**
   * 从服务器获取示例项目
   */
  private async fetchExampleProjects(): Promise<ExampleProject[]> {
    try {
      const response = await fetch('/api/examples');
      if (!response.ok) {
        throw new Error(`Failed to fetch example projects: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching example projects:', error);
      return [];
    }
  }

  /**
   * 加载用户数据
   */
  private loadUserData(): void {
    try {
      // 加载已查看的示例项目
      const viewedExamplesJson = localStorage.getItem('viewedExamples');
      if (viewedExamplesJson) {
        this.viewedExamples = new Set(JSON.parse(viewedExamplesJson));
      }

      // 加载收藏的示例项目
      const favoriteExamplesJson = localStorage.getItem('favoriteExamples');
      if (favoriteExamplesJson) {
        this.favoriteExamples = new Set(JSON.parse(favoriteExamplesJson));
      }

      // 加载最近查看的示例项目
      const recentlyViewedJson = localStorage.getItem('recentlyViewedExamples');
      if (recentlyViewedJson) {
        this.recentlyViewedExamples = JSON.parse(recentlyViewedJson);
      }
    } catch (error) {
      console.error('Failed to load user data:', error);
    }
  }

  /**
   * 保存用户数据
   */
  private saveUserData(): void {
    try {
      // 保存已查看的示例项目
      localStorage.setItem('viewedExamples', JSON.stringify([...this.viewedExamples]));

      // 保存收藏的示例项目
      localStorage.setItem('favoriteExamples', JSON.stringify([...this.favoriteExamples]));

      // 保存最近查看的示例项目
      localStorage.setItem('recentlyViewedExamples', JSON.stringify(this.recentlyViewedExamples));
    } catch (error) {
      console.error('Failed to save user data:', error);
    }
  }

  /**
   * 获取所有示例项目
   */
  public getExampleProjects(): ExampleProject[] {
    return this.examples;
  }

  /**
   * 根据ID获取示例项目
   */
  public getExampleProjectById(id: string): ExampleProject | undefined {
    return this.examples.find(example => example.id === id);
  }

  /**
   * 根据类别获取示例项目
   */
  public getExampleProjectsByCategory(category: string): ExampleProject[] {
    return this.examples.filter(example => example.category === category);
  }

  /**
   * 根据难度获取示例项目
   */
  public getExampleProjectsByDifficulty(difficulty: string): ExampleProject[] {
    return this.examples.filter(example => example.difficulty === difficulty);
  }

  /**
   * 根据标签获取示例项目
   */
  public getExampleProjectsByTag(tag: string): ExampleProject[] {
    return this.examples.filter(example => example.tags?.includes(tag));
  }

  /**
   * 搜索示例项目
   */
  public searchExampleProjects(query: string): ExampleProject[] {
    const lowerQuery = query.toLowerCase();
    return this.examples.filter(example =>
      example.title.toLowerCase().includes(lowerQuery) ||
      example.description.toLowerCase().includes(lowerQuery) ||
      example.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * 获取推荐示例项目
   */
  public getRecommendedExampleProjects(count: number = 5): ExampleProject[] {
    // 基于用户查看历史和收藏推荐示例项目
    const scoredExamples = this.examples.map(example => {
      let score = 0;

      // 已查看的示例项目得分较低
      if (this.viewedExamples.has(example.id)) {
        score -= 10;
      }

      // 收藏的示例项目得分较高
      if (this.favoriteExamples.has(example.id)) {
        score += 5;
      }

      // 基于难度的分数
      switch (example.difficulty) {
        case 'beginner':
          score += 10;
          break;
        case 'intermediate':
          score += 5;
          break;
        case 'advanced':
          score += 2;
          break;
        case 'expert':
          score += 1;
          break;
      }

      // 基于受欢迎程度的分数
      if (example.popularity) {
        score += example.popularity / 10;
      }

      // 基于评分的分数
      if (example.rating) {
        score += example.rating * 2;
      }

      return { example, score };
    });

    // 按分数降序排序
    scoredExamples.sort((a, b) => b.score - a.score);

    // 返回前N个示例项目
    return scoredExamples.slice(0, count).map(scored => scored.example);
  }

  /**
   * 获取最近查看的示例项目
   */
  public getRecentlyViewedExampleProjects(count: number = 5): ExampleProject[] {
    return this.recentlyViewedExamples
      .slice(0, count)
      .map(id => this.getExampleProjectById(id))
      .filter(example => example !== undefined) as ExampleProject[];
  }

  /**
   * 获取收藏的示例项目
   */
  public getFavoriteExampleProjects(): ExampleProject[] {
    return this.examples.filter(example => this.favoriteExamples.has(example.id));
  }

  /**
   * 标记示例项目为已查看
   */
  public markExampleAsViewed(id: string): void {
    this.viewedExamples.add(id);

    // 更新最近查看列表
    this.recentlyViewedExamples = this.recentlyViewedExamples.filter(exampleId => exampleId !== id);
    this.recentlyViewedExamples.unshift(id);

    // 限制最近查看列表长度
    if (this.recentlyViewedExamples.length > 20) {
      this.recentlyViewedExamples = this.recentlyViewedExamples.slice(0, 20);
    }

    this.saveUserData();
    this.events.emit('exampleViewed', id);
  }

  /**
   * 收藏示例项目
   */
  public favoriteExample(id: string): void {
    this.favoriteExamples.add(id);
    this.saveUserData();
    this.events.emit('exampleFavorited', id);
  }

  /**
   * 取消收藏示例项目
   */
  public unfavoriteExample(id: string): void {
    this.favoriteExamples.delete(id);
    this.saveUserData();
    this.events.emit('exampleUnfavorited', id);
  }

  /**
   * 检查示例项目是否已收藏
   */
  public isExampleFavorited(id: string): boolean {
    return this.favoriteExamples.has(id);
  }

  /**
   * 检查示例项目是否已查看
   */
  public isExampleViewed(id: string): boolean {
    return this.viewedExamples.has(id);
  }

  /**
   * 导入示例项目
   */
  public async importExampleProject(id: string, projectName?: string): Promise<boolean> {
    const example = this.getExampleProjectById(id);
    if (!example) {
      console.error(`Example project with ID ${id} not found`);
      return false;
    }

    try {
      const response = await fetch(`/api/examples/import`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          exampleId: id,
          projectName: projectName || example.title,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to import example project: ${response.statusText}`);
      }

      const result = await response.json();
      this.events.emit('exampleImported', id, result.projectId);
      return true;
    } catch (error) {
      console.error('Error importing example project:', error);
      return false;
    }
  }

  /**
   * 添加事件监听器
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.events.on(event, listener);
  }

  /**
   * 移除事件监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.events.off(event, listener);
  }
}

// 导出单例实例
export const exampleProjectService = ExampleProjectService.getInstance();
