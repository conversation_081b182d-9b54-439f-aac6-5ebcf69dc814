/**
 * 内存分析面板组件
 * 用于分析和显示内存使用情况
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Row, Col, Table, Progress, Statistic, Divider, Typography, Space, Button, Tabs, Alert, Switch, Select, Radio, Tooltip, Modal, Empty } from 'antd';
import { useTranslation } from 'react-i18next';
import { Pie, Line } from '@ant-design/charts';
import {
  MemoryOutlined,
  FileImageOutlined,
  AppstoreOutlined,
  CodeOutlined,
  ReloadOutlined,
  WarningOutlined,
  CameraOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  LineChartOutlined,
  FilterOutlined,
  SearchOutlined,
  SyncOutlined,
  InfoCircleOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { PerformanceMonitor, PerformanceMetricType } from 'dl-engine-core/utils/PerformanceMonitor';
import { MemoryAnalyzer, ResourceType, ResourceInfo, MemorySnapshot } from 'dl-engine-core/utils/MemoryAnalyzer';
import { ResourceTracker } from 'dl-engine-core/utils/ResourceTracker';
import { useAppDispatch, useAppSelector } from '../../store';
import { setMemoryMonitoringEnabled } from '../../store/debug/debugSlice';
import './MemoryAnalysisPanel.less';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { confirm } = Modal;

interface MemoryItem {
  key: string;
  id: string;
  name: string;
  size: number;
  count: number;
  type: string;
  createdAt: number;
  lastAccessTime: number;
  disposed: boolean;
}

interface MemoryAnalysisProps {
  className?: string;
}

/**
 * 内存分析面板组件
 */
const MemoryAnalysisPanel: React.FC<MemoryAnalysisProps> = ({ className }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const memoryMonitoringEnabled = useAppSelector(state => state.debug.memoryMonitoringEnabled);

  // 引用
  const memoryAnalyzer = useRef(MemoryAnalyzer.getInstance());
  const resourceTracker = useRef(ResourceTracker.getInstance());
  const autoRefreshTimerRef = useRef<number | null>(null);

  // 状态
  const [activeTab, setActiveTab] = useState('overview');
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(5000); // 5秒
  const [resourceTypeFilter, setResourceTypeFilter] = useState<ResourceType | 'all'>('all');
  const [searchText, setSearchText] = useState('');
  const [selectedResourceId, setSelectedResourceId] = useState<string | null>(null);
  const [selectedSnapshot, setSelectedSnapshot] = useState<string | null>(null);
  const [compareSnapshot, setCompareSnapshot] = useState<string | null>(null);
  const [showDisposed, setShowDisposed] = useState(false);
  const [leakDetectionEnabled, setLeakDetectionEnabled] = useState(true);

  // 内存数据状态
  const [memoryData, setMemoryData] = useState<{
    total: number;
    js: number;
    textures: number;
    geometries: number;
    materials: number;
    models: number;
    other: number;
    items: MemoryItem[];
    snapshots: MemorySnapshot[];
    potentialLeaks: ResourceInfo[];
    totalLeakedMemory: number;
    memoryHistory: Array<{ timestamp: number; value: number }>;
  }>({
    total: 0,
    js: 0,
    textures: 0,
    geometries: 0,
    materials: 0,
    models: 0,
    other: 0,
    items: [],
    snapshots: [],
    potentialLeaks: [],
    totalLeakedMemory: 0,
    memoryHistory: [],
  });

  // 初始化
  useEffect(() => {
    if (memoryMonitoringEnabled) {
      startMonitoring();
    }

    return () => {
      stopMonitoring();
    };
  }, [memoryMonitoringEnabled]);

  // 启动监控
  const startMonitoring = () => {
    if (isMonitoring) return;

    // 配置内存分析器
    memoryAnalyzer.current.configure({
      enabled: true,
      enableAutoSnapshot: true,
      autoSnapshotInterval: 60000, // 1分钟
      enableLeakDetection: leakDetectionEnabled,
      debug: false,
      collectDetailedInfo: true,
      enableWarnings: true
    });

    // 配置资源跟踪器
    resourceTracker.current.configure({
      enabled: true,
      autoTrackAll: true,
      autoCalculateSize: true
    });

    // 启动内存分析器和资源跟踪器
    memoryAnalyzer.current.start();
    resourceTracker.current.start();

    // 注册事件监听器
    memoryAnalyzer.current.on('snapshotTaken', updateMemoryData);
    memoryAnalyzer.current.on('leaksDetected', updateMemoryData);
    memoryAnalyzer.current.on('resourceRegistered', updateMemoryData);
    memoryAnalyzer.current.on('resourceDisposed', updateMemoryData);

    // 立即更新数据
    updateMemoryData();

    setIsMonitoring(true);
    dispatch(setMemoryMonitoringEnabled(true));
  };

  // 停止监控
  const stopMonitoring = () => {
    if (!isMonitoring) return;

    // 停止内存分析器
    memoryAnalyzer.current.stop();

    // 移除事件监听器
    memoryAnalyzer.current.off('snapshotTaken', updateMemoryData);
    memoryAnalyzer.current.off('leaksDetected', updateMemoryData);
    memoryAnalyzer.current.off('resourceRegistered', updateMemoryData);
    memoryAnalyzer.current.off('resourceDisposed', updateMemoryData);

    // 停止自动刷新
    if (autoRefreshTimerRef.current !== null) {
      window.clearInterval(autoRefreshTimerRef.current);
      autoRefreshTimerRef.current = null;
    }

    setIsMonitoring(false);
    dispatch(setMemoryMonitoringEnabled(false));
  };

  // 切换自动刷新
  useEffect(() => {
    if (autoRefresh) {
      autoRefreshTimerRef.current = window.setInterval(() => {
        updateMemoryData();
      }, refreshInterval);
    } else if (autoRefreshTimerRef.current !== null) {
      window.clearInterval(autoRefreshTimerRef.current);
      autoRefreshTimerRef.current = null;
    }

    return () => {
      if (autoRefreshTimerRef.current !== null) {
        window.clearInterval(autoRefreshTimerRef.current);
        autoRefreshTimerRef.current = null;
      }
    };
  }, [autoRefresh, refreshInterval]);

  // 更新内存数据
  const updateMemoryData = () => {
    if (!isMonitoring) return;

    // 获取内存使用情况
    const memoryUsage = memoryAnalyzer.current.getMemoryUsage();
    const jsHeapMemory = memoryUsage.jsHeap / (1024 * 1024); // 转换为MB
    const resourceMemory = memoryUsage.resources / (1024 * 1024); // 转换为MB

    // 获取按类型分组的内存使用量
    const memoryByType = memoryUsage.byType;
    const textureMemory = memoryByType[ResourceType.TEXTURE] / (1024 * 1024);
    const geometryMemory = memoryByType[ResourceType.GEOMETRY] / (1024 * 1024);
    const materialMemory = memoryByType[ResourceType.MATERIAL] / (1024 * 1024);
    const modelMemory = memoryByType[ResourceType.MODEL] / (1024 * 1024);
    const otherMemory = (memoryByType[ResourceType.OTHER] + memoryByType[ResourceType.SHADER] +
                         memoryByType[ResourceType.AUDIO] + memoryByType[ResourceType.ANIMATION]) / (1024 * 1024);

    // 获取资源列表
    const resources = memoryAnalyzer.current.getActiveResources();
    const disposedResources = showDisposed ?
      memoryAnalyzer.current.getAllResources().filter(r => r.disposed) : [];

    const allResources = [...resources, ...disposedResources];

    // 过滤资源
    const filteredResources = allResources.filter(resource => {
      // 类型过滤
      if (resourceTypeFilter !== 'all' && resource.type !== resourceTypeFilter) {
        return false;
      }

      // 搜索过滤
      if (searchText && !resource.name.toLowerCase().includes(searchText.toLowerCase()) &&
          !resource.id.toLowerCase().includes(searchText.toLowerCase())) {
        return false;
      }

      return true;
    });

    // 转换为表格项
    const items: MemoryItem[] = filteredResources.map(resource => ({
      key: resource.id,
      id: resource.id,
      name: resource.name,
      size: resource.size / (1024 * 1024), // 转换为MB
      count: resource.refCount,
      type: resource.type,
      createdAt: resource.createdAt,
      lastAccessTime: resource.lastAccessTime,
      disposed: resource.disposed
    }));

    // 获取快照列表
    const snapshots = memoryAnalyzer.current.getSnapshots();

    // 检测内存泄漏
    const { potentialLeaks, totalLeakedMemory } = memoryAnalyzer.current.detectLeaks();

    // 更新内存历史数据
    const now = Date.now();
    const newHistoryPoint = { timestamp: now, value: jsHeapMemory };

    // 保留最近30个数据点
    const memoryHistory = [...memoryData.memoryHistory, newHistoryPoint]
      .slice(-30);

    setMemoryData({
      total: jsHeapMemory,
      js: jsHeapMemory - resourceMemory,
      textures: textureMemory,
      geometries: geometryMemory,
      materials: materialMemory,
      models: modelMemory,
      other: otherMemory,
      items,
      snapshots,
      potentialLeaks,
      totalLeakedMemory,
      memoryHistory
    });
  };

  // 表格列定义
  const columns = [
    {
      title: t('debug.memory.name'),
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      render: (text: string, record: MemoryItem) => (
        <Tooltip title={text}>
          <span className={record.disposed ? 'memory-disposed' : ''}>
            {text}
          </span>
        </Tooltip>
      ),
    },
    {
      title: t('debug.memory.id'),
      dataIndex: 'id',
      key: 'id',
      ellipsis: true,
      width: 100,
      render: (text: string) => (
        <Tooltip title={text}>
          <span>{text.substring(0, 8)}...</span>
        </Tooltip>
      ),
    },
    {
      title: t('debug.memory.size'),
      dataIndex: 'size',
      key: 'size',
      width: 100,
      render: (size: number, record: MemoryItem) => (
        <span className={record.disposed ? 'memory-disposed' : ''}>
          {size.toFixed(2)} MB
        </span>
      ),
      sorter: (a: MemoryItem, b: MemoryItem) => a.size - b.size,
    },
    {
      title: t('debug.memory.count'),
      dataIndex: 'count',
      key: 'count',
      width: 80,
      render: (count: number, record: MemoryItem) => (
        <span className={record.disposed ? 'memory-disposed' : ''}>
          {count}
        </span>
      ),
      sorter: (a: MemoryItem, b: MemoryItem) => a.count - b.count,
    },
    {
      title: t('debug.memory.type'),
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string, record: MemoryItem) => (
        <Tag color={getTypeColor(type)} className={record.disposed ? 'memory-disposed' : ''}>
          {type.toUpperCase()}
        </Tag>
      ),
      filters: Object.values(ResourceType).map(type => ({ text: type.toUpperCase(), value: type })),
      onFilter: (value: string, record: MemoryItem) => record.type === value,
    },
    {
      title: t('debug.memory.created'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (time: number) => new Date(time).toLocaleString(),
      sorter: (a: MemoryItem, b: MemoryItem) => a.createdAt - b.createdAt,
    },
    {
      title: t('debug.memory.lastAccess'),
      dataIndex: 'lastAccessTime',
      key: 'lastAccessTime',
      width: 150,
      render: (time: number) => new Date(time).toLocaleString(),
      sorter: (a: MemoryItem, b: MemoryItem) => a.lastAccessTime - b.lastAccessTime,
    },
    {
      title: t('debug.memory.status'),
      dataIndex: 'disposed',
      key: 'disposed',
      width: 100,
      render: (disposed: boolean) => (
        disposed ?
          <Tag color="red">{t('debug.memory.disposed')}</Tag> :
          <Tag color="green">{t('debug.memory.active')}</Tag>
      ),
      filters: [
        { text: t('debug.memory.active'), value: false },
        { text: t('debug.memory.disposed'), value: true },
      ],
      onFilter: (value: boolean, record: MemoryItem) => record.disposed === value,
    },
  ];

  // 获取资源类型颜色
  const getTypeColor = (type: string): string => {
    switch (type) {
      case ResourceType.TEXTURE:
        return 'blue';
      case ResourceType.GEOMETRY:
        return 'green';
      case ResourceType.MATERIAL:
        return 'purple';
      case ResourceType.MODEL:
        return 'orange';
      case ResourceType.SHADER:
        return 'cyan';
      case ResourceType.AUDIO:
        return 'magenta';
      case ResourceType.ANIMATION:
        return 'gold';
      default:
        return 'default';
    }
  };

  // 饼图数据
  const pieData = [
    { type: t('debug.memory.textures'), value: memoryData.textures },
    { type: t('debug.memory.geometries'), value: memoryData.geometries },
    { type: t('debug.memory.materials'), value: memoryData.materials },
    { type: t('debug.memory.models'), value: memoryData.models },
    { type: t('debug.memory.other'), value: memoryData.other },
    { type: t('debug.memory.js'), value: memoryData.js },
  ];

  // 内存历史图表配置
  const memoryHistoryConfig = {
    data: memoryData.memoryHistory,
    xField: 'timestamp',
    yField: 'value',
    seriesField: 'type',
    xAxis: {
      type: 'time',
      tickCount: 5,
    },
    yAxis: {
      label: {
        formatter: (v: string) => `${v} MB`,
      },
    },
    tooltip: {
      formatter: (data: any) => {
        return { name: t('debug.memory.total'), value: `${data.value.toFixed(2)} MB` };
      },
    },
  };

  // 拍摄内存快照
  const takeSnapshot = () => {
    const snapshot = memoryAnalyzer.current.takeSnapshot();
    setSelectedSnapshot(snapshot.id);
    updateMemoryData();
  };

  // 清除所有快照
  const clearSnapshots = () => {
    confirm({
      title: t('debug.memory.confirmClearSnapshots'),
      icon: <ExclamationCircleOutlined />,
      content: t('debug.memory.confirmClearSnapshotsContent'),
      onOk() {
        memoryAnalyzer.current.clearSnapshots();
        setSelectedSnapshot(null);
        setCompareSnapshot(null);
        updateMemoryData();
      },
    });
  };

  // 检测内存泄漏
  const detectLeaks = () => {
    const result = memoryAnalyzer.current.detectLeaks();
    updateMemoryData();
    return result;
  };

  // 格式化字节数
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化时间差
  const formatTimeDiff = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;

    if (diff < 60000) {
      return `${Math.floor(diff / 1000)}${t('debug.memory.secondsAgo')}`;
    } else if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}${t('debug.memory.minutesAgo')}`;
    } else if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}${t('debug.memory.hoursAgo')}`;
    } else {
      return `${Math.floor(diff / 86400000)}${t('debug.memory.daysAgo')}`;
    }
  };

  return (
    <div className={`memory-analysis-panel ${className || ''}`}>
      <div className="memory-toolbar">
        <Space>
          <Button
            type={isMonitoring ? "primary" : "default"}
            icon={isMonitoring ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            onClick={() => isMonitoring ? stopMonitoring() : startMonitoring()}
          >
            {isMonitoring ? t('debug.memory.stopMonitoring') : t('debug.memory.startMonitoring')}
          </Button>

          <Button icon={<ReloadOutlined />} onClick={updateMemoryData}>
            {t('debug.memory.refresh')}
          </Button>

          <Switch
            checkedChildren={t('debug.memory.autoRefreshOn')}
            unCheckedChildren={t('debug.memory.autoRefreshOff')}
            checked={autoRefresh}
            onChange={setAutoRefresh}
          />

          {autoRefresh && (
            <Select
              value={refreshInterval}
              onChange={setRefreshInterval}
              style={{ width: 120 }}
            >
              <Option value={1000}>{t('debug.memory.interval1s')}</Option>
              <Option value={5000}>{t('debug.memory.interval5s')}</Option>
              <Option value={10000}>{t('debug.memory.interval10s')}</Option>
              <Option value={30000}>{t('debug.memory.interval30s')}</Option>
            </Select>
          )}

          <Button icon={<CameraOutlined />} onClick={takeSnapshot}>
            {t('debug.memory.takeSnapshot')}
          </Button>

          <Button icon={<WarningOutlined />} onClick={detectLeaks}>
            {t('debug.memory.detectLeaks')}
          </Button>
        </Space>
      </div>

      <div className="memory-content">
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={t('debug.memory.tabs.overview')} key="overview">
            {/* 内存概览 */}
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Card>
                  <Title level={4}>{t('debug.memory.overview')}</Title>
                  <Row gutter={[16, 16]}>
                    <Col span={4}>
                      <Statistic
                        title={t('debug.memory.total')}
                        value={memoryData.total}
                        precision={2}
                        suffix="MB"
                        prefix={<MemoryOutlined />}
                        valueStyle={{ color: memoryData.total > 500 ? '#cf1322' : '#3f8600' }}
                      />
                    </Col>
                    <Col span={4}>
                      <Statistic
                        title={t('debug.memory.textures')}
                        value={memoryData.textures}
                        precision={2}
                        suffix="MB"
                        prefix={<FileImageOutlined />}
                        valueStyle={{ color: memoryData.textures > 250 ? '#cf1322' : '#3f8600' }}
                      />
                    </Col>
                    <Col span={4}>
                      <Statistic
                        title={t('debug.memory.geometries')}
                        value={memoryData.geometries}
                        precision={2}
                        suffix="MB"
                        prefix={<AppstoreOutlined />}
                        valueStyle={{ color: memoryData.geometries > 250 ? '#cf1322' : '#3f8600' }}
                      />
                    </Col>
                    <Col span={4}>
                      <Statistic
                        title={t('debug.memory.materials')}
                        value={memoryData.materials}
                        precision={2}
                        suffix="MB"
                        prefix={<AppstoreOutlined />}
                        valueStyle={{ color: memoryData.materials > 100 ? '#cf1322' : '#3f8600' }}
                      />
                    </Col>
                    <Col span={4}>
                      <Statistic
                        title={t('debug.memory.models')}
                        value={memoryData.models}
                        precision={2}
                        suffix="MB"
                        prefix={<AppstoreOutlined />}
                        valueStyle={{ color: memoryData.models > 200 ? '#cf1322' : '#3f8600' }}
                      />
                    </Col>
                    <Col span={4}>
                      <Statistic
                        title={t('debug.memory.js')}
                        value={memoryData.js}
                        precision={2}
                        suffix="MB"
                        prefix={<CodeOutlined />}
                        valueStyle={{ color: memoryData.js > 200 ? '#cf1322' : '#3f8600' }}
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>
            </Row>

            <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
              <Col span={12}>
                <Card title={t('debug.memory.distribution')}>
                  <Pie
                    data={pieData}
                    angleField="value"
                    colorField="type"
                    radius={0.8}
                    label={{
                      type: 'outer',
                      content: '{name}: {percentage}',
                    }}
                    interactions={[{ type: 'element-active' }]}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card title={t('debug.memory.usage')}>
                  <div className="memory-usage">
                    <div className="memory-usage-item">
                      <Text>{t('debug.memory.total')}</Text>
                      <Progress
                        percent={(memoryData.total / 1000) * 100}
                        status={memoryData.total > 500 ? 'exception' : 'normal'}
                        format={(percent) => `${memoryData.total.toFixed(2)} MB`}
                      />
                    </div>
                    <div className="memory-usage-item">
                      <Text>{t('debug.memory.textures')}</Text>
                      <Progress
                        percent={(memoryData.textures / 500) * 100}
                        status={memoryData.textures > 250 ? 'exception' : 'normal'}
                        format={(percent) => `${memoryData.textures.toFixed(2)} MB`}
                      />
                    </div>
                    <div className="memory-usage-item">
                      <Text>{t('debug.memory.geometries')}</Text>
                      <Progress
                        percent={(memoryData.geometries / 500) * 100}
                        status={memoryData.geometries > 250 ? 'exception' : 'normal'}
                        format={(percent) => `${memoryData.geometries.toFixed(2)} MB`}
                      />
                    </div>
                    <div className="memory-usage-item">
                      <Text>{t('debug.memory.materials')}</Text>
                      <Progress
                        percent={(memoryData.materials / 200) * 100}
                        status={memoryData.materials > 100 ? 'exception' : 'normal'}
                        format={(percent) => `${memoryData.materials.toFixed(2)} MB`}
                      />
                    </div>
                    <div className="memory-usage-item">
                      <Text>{t('debug.memory.models')}</Text>
                      <Progress
                        percent={(memoryData.models / 400) * 100}
                        status={memoryData.models > 200 ? 'exception' : 'normal'}
                        format={(percent) => `${memoryData.models.toFixed(2)} MB`}
                      />
                    </div>
                    <div className="memory-usage-item">
                      <Text>{t('debug.memory.js')}</Text>
                      <Progress
                        percent={(memoryData.js / 400) * 100}
                        status={memoryData.js > 200 ? 'exception' : 'normal'}
                        format={(percent) => `${memoryData.js.toFixed(2)} MB`}
                      />
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>

            <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
              <Col span={24}>
                <Card title={t('debug.memory.memoryTrend')}>
                  <Line {...memoryHistoryConfig} />
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab={t('debug.memory.tabs.resources')} key="resources">
            {/* 资源列表 */}
            <Card>
              <div className="resource-filter">
                <Space>
                  <Select
                    value={resourceTypeFilter}
                    onChange={setResourceTypeFilter}
                    style={{ width: 150 }}
                  >
                    <Option value="all">{t('debug.memory.allTypes')}</Option>
                    {Object.values(ResourceType).map(type => (
                      <Option key={type} value={type}>{type.toUpperCase()}</Option>
                    ))}
                  </Select>

                  <Input.Search
                    placeholder={t('debug.memory.searchResources')}
                    value={searchText}
                    onChange={e => setSearchText(e.target.value)}
                    style={{ width: 250 }}
                    allowClear
                  />

                  <Switch
                    checkedChildren={t('debug.memory.showDisposed')}
                    unCheckedChildren={t('debug.memory.hideDisposed')}
                    checked={showDisposed}
                    onChange={setShowDisposed}
                  />
                </Space>
              </div>

              <Table
                dataSource={memoryData.items}
                columns={columns}
                pagination={{ pageSize: 10 }}
                size="middle"
                rowClassName={record => record.disposed ? 'memory-disposed-row' : ''}
                onRow={record => ({
                  onClick: () => setSelectedResourceId(record.id),
                  className: selectedResourceId === record.id ? 'selected-row' : ''
                })}
              />
            </Card>
          </TabPane>

          <TabPane tab={t('debug.memory.tabs.leaks')} key="leaks">
            {/* 内存泄漏检测 */}
            <Card>
              <div className="leak-controls">
                <Space>
                  <Button type="primary" icon={<WarningOutlined />} onClick={detectLeaks}>
                    {t('debug.memory.detectLeaks')}
                  </Button>

                  <Switch
                    checkedChildren={t('debug.memory.leakDetectionOn')}
                    unCheckedChildren={t('debug.memory.leakDetectionOff')}
                    checked={leakDetectionEnabled}
                    onChange={checked => {
                      setLeakDetectionEnabled(checked);
                      memoryAnalyzer.current.configure({ enableLeakDetection: checked });
                    }}
                  />
                </Space>
              </div>

              {memoryData.potentialLeaks.length > 0 ? (
                <>
                  <Alert
                    message={t('debug.memory.leaksDetected')}
                    description={t('debug.memory.leaksDescription', { count: memoryData.potentialLeaks.length, size: formatBytes(memoryData.totalLeakedMemory) })}
                    type="warning"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />

                  <Table
                    dataSource={memoryData.potentialLeaks.map(leak => ({
                      key: leak.id,
                      id: leak.id,
                      name: leak.name,
                      size: leak.size / (1024 * 1024), // 转换为MB
                      count: leak.refCount,
                      type: leak.type,
                      createdAt: leak.createdAt,
                      lastAccessTime: leak.lastAccessTime,
                      disposed: leak.disposed,
                      age: formatTimeDiff(leak.createdAt)
                    }))}
                    columns={[
                      ...columns,
                      {
                        title: t('debug.memory.age'),
                        dataIndex: 'age',
                        key: 'age',
                        width: 100
                      }
                    ]}
                    pagination={{ pageSize: 10 }}
                    size="middle"
                  />
                </>
              ) : (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={t('debug.memory.noLeaksDetected')}
                />
              )}
            </Card>
          </TabPane>

          <TabPane tab={t('debug.memory.tabs.snapshots')} key="snapshots">
            {/* 内存快照 */}
            <Card>
              <div className="snapshot-controls">
                <Space>
                  <Button type="primary" icon={<CameraOutlined />} onClick={takeSnapshot}>
                    {t('debug.memory.takeSnapshot')}
                  </Button>

                  <Button icon={<DeleteOutlined />} onClick={clearSnapshots} disabled={memoryData.snapshots.length === 0}>
                    {t('debug.memory.clearSnapshots')}
                  </Button>
                </Space>
              </div>

              {memoryData.snapshots.length > 0 ? (
                <>
                  <div className="snapshot-selection" style={{ marginTop: 16 }}>
                    <Row gutter={16}>
                      <Col span={12}>
                        <div className="snapshot-select">
                          <Text>{t('debug.memory.selectSnapshot')}</Text>
                          <Select
                            value={selectedSnapshot}
                            onChange={setSelectedSnapshot}
                            style={{ width: '100%', marginTop: 8 }}
                            placeholder={t('debug.memory.selectSnapshotPlaceholder')}
                          >
                            {memoryData.snapshots.map(snapshot => (
                              <Option key={snapshot.id} value={snapshot.id}>
                                {new Date(snapshot.timestamp).toLocaleString()} ({formatBytes(snapshot.totalMemory)})
                              </Option>
                            ))}
                          </Select>
                        </div>
                      </Col>
                      <Col span={12}>
                        <div className="snapshot-compare">
                          <Text>{t('debug.memory.compareWith')}</Text>
                          <Select
                            value={compareSnapshot}
                            onChange={setCompareSnapshot}
                            style={{ width: '100%', marginTop: 8 }}
                            placeholder={t('debug.memory.selectCompareSnapshotPlaceholder')}
                            allowClear
                          >
                            {memoryData.snapshots
                              .filter(snapshot => snapshot.id !== selectedSnapshot)
                              .map(snapshot => (
                                <Option key={snapshot.id} value={snapshot.id}>
                                  {new Date(snapshot.timestamp).toLocaleString()} ({formatBytes(snapshot.totalMemory)})
                                </Option>
                              ))}
                          </Select>
                        </div>
                      </Col>
                    </Row>
                  </div>

                  {selectedSnapshot && (
                    <div className="snapshot-details" style={{ marginTop: 16 }}>
                      {(() => {
                        const snapshot = memoryData.snapshots.find(s => s.id === selectedSnapshot);
                        if (!snapshot) return null;

                        if (compareSnapshot) {
                          // 比较两个快照
                          const compareSnapshotObj = memoryData.snapshots.find(s => s.id === compareSnapshot);
                          if (!compareSnapshotObj) return null;

                          const comparison = memoryAnalyzer.current.compareSnapshots(selectedSnapshot, compareSnapshot);

                          return (
                            <>
                              <Alert
                                message={t('debug.memory.comparisonResult')}
                                description={
                                  <>
                                    <p>{t('debug.memory.snapshotTimeRange', {
                                      start: new Date(snapshot.timestamp).toLocaleString(),
                                      end: new Date(compareSnapshotObj.timestamp).toLocaleString()
                                    })}</p>
                                    <p>{t('debug.memory.totalMemoryChange', {
                                      change: formatBytes(comparison.totalDiff),
                                      percent: ((comparison.totalDiff / snapshot.totalMemory) * 100).toFixed(2)
                                    })}</p>
                                    <p>{t('debug.memory.newResources', { count: comparison.newResources.length })}</p>
                                    <p>{t('debug.memory.disposedResources', { count: comparison.disposedResources.length })}</p>
                                  </>
                                }
                                type={comparison.totalDiff > 0 ? 'warning' : 'success'}
                                showIcon
                              />

                              <Tabs defaultActiveKey="changes" style={{ marginTop: 16 }}>
                                <TabPane tab={t('debug.memory.memoryChanges')} key="changes">
                                  <Row gutter={[16, 16]}>
                                    {Object.entries(comparison.typeDiffs).map(([type, diff]) => (
                                      <Col span={8} key={type}>
                                        <Card size="small">
                                          <Statistic
                                            title={type.toUpperCase()}
                                            value={diff / (1024 * 1024)} // 转换为MB
                                            precision={2}
                                            valueStyle={{ color: diff > 0 ? '#cf1322' : '#3f8600' }}
                                            prefix={diff > 0 ? <WarningOutlined /> : <InfoCircleOutlined />}
                                            suffix="MB"
                                          />
                                        </Card>
                                      </Col>
                                    ))}
                                  </Row>
                                </TabPane>
                                <TabPane tab={t('debug.memory.newResources')} key="new">
                                  <Table
                                    dataSource={comparison.newResources.map(resource => ({
                                      key: resource.id,
                                      id: resource.id,
                                      name: resource.name,
                                      size: resource.size / (1024 * 1024), // 转换为MB
                                      count: resource.refCount,
                                      type: resource.type,
                                      createdAt: resource.createdAt
                                    }))}
                                    columns={columns.filter(col => col.key !== 'disposed' && col.key !== 'lastAccessTime')}
                                    pagination={{ pageSize: 10 }}
                                    size="small"
                                  />
                                </TabPane>
                                <TabPane tab={t('debug.memory.disposedResources')} key="disposed">
                                  <Table
                                    dataSource={comparison.disposedResources.map(resource => ({
                                      key: resource.id,
                                      id: resource.id,
                                      name: resource.name,
                                      size: resource.size / (1024 * 1024), // 转换为MB
                                      count: resource.refCount,
                                      type: resource.type,
                                      createdAt: resource.createdAt
                                    }))}
                                    columns={columns.filter(col => col.key !== 'disposed' && col.key !== 'lastAccessTime')}
                                    pagination={{ pageSize: 10 }}
                                    size="small"
                                  />
                                </TabPane>
                              </Tabs>
                            </>
                          );
                        } else {
                          // 显示单个快照
                          return (
                            <>
                              <Card>
                                <Row gutter={[16, 16]}>
                                  <Col span={6}>
                                    <Statistic
                                      title={t('debug.memory.snapshotTime')}
                                      value={new Date(snapshot.timestamp).toLocaleString()}
                                      prefix={<ClockCircleOutlined />}
                                    />
                                  </Col>
                                  <Col span={6}>
                                    <Statistic
                                      title={t('debug.memory.totalMemory')}
                                      value={(snapshot.totalMemory / (1024 * 1024)).toFixed(2)}
                                      suffix="MB"
                                      prefix={<MemoryOutlined />}
                                    />
                                  </Col>
                                  <Col span={6}>
                                    <Statistic
                                      title={t('debug.memory.resourceCount')}
                                      value={snapshot.resources.length}
                                      prefix={<AppstoreOutlined />}
                                    />
                                  </Col>
                                  <Col span={6}>
                                    <Statistic
                                      title={t('debug.memory.resourceMemory')}
                                      value={(snapshot.resourceMemory / (1024 * 1024)).toFixed(2)}
                                      suffix="MB"
                                      prefix={<FileImageOutlined />}
                                    />
                                  </Col>
                                </Row>
                              </Card>

                              <Card title={t('debug.memory.resourceDistribution')} style={{ marginTop: 16 }}>
                                <Pie
                                  data={Object.entries(snapshot.memoryByType).map(([type, value]) => ({
                                    type,
                                    value: value / (1024 * 1024) // 转换为MB
                                  }))}
                                  angleField="value"
                                  colorField="type"
                                  radius={0.8}
                                  label={{
                                    type: 'outer',
                                    content: '{name}: {percentage}',
                                  }}
                                  interactions={[{ type: 'element-active' }]}
                                />
                              </Card>

                              {snapshot.resources.length > 0 && (
                                <Card title={t('debug.memory.resourceDetails')} style={{ marginTop: 16 }}>
                                  <Table
                                    dataSource={snapshot.resources.map(resource => ({
                                      key: resource.id,
                                      id: resource.id,
                                      name: resource.name,
                                      size: resource.size / (1024 * 1024), // 转换为MB
                                      count: resource.refCount,
                                      type: resource.type,
                                      createdAt: resource.createdAt,
                                      lastAccessTime: resource.lastAccessTime,
                                      disposed: resource.disposed
                                    }))}
                                    columns={columns}
                                    pagination={{ pageSize: 10 }}
                                    size="small"
                                  />
                                </Card>
                              )}
                            </>
                          );
                        }
                      })()}
                    </div>
                  )}
                </>
              ) : (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={t('debug.memory.noSnapshots')}
                />
              )}
            </Card>
          </TabPane>
        </Tabs>
      </div>
    </div>
  );
};

export default MemoryAnalysisPanel;
