/**
 * DL Engine 类型声明文件
 * 为 dl-engine.mjs 提供 TypeScript 类型支持
 */

// 依赖类型枚举
export enum DependencyType {
  STRONG = 'strong',
  WEAK = 'weak',
  LAZY = 'lazy'
}

// 资源类型
export type AssetType = 
  | 'texture'
  | 'model'
  | 'material'
  | 'shader'
  | 'audio'
  | 'scene'
  | 'animation'
  | 'script'
  | 'font'
  | 'video'
  | 'data';

// 依赖信息接口
export interface DependencyInfo {
  id: string;
  type: DependencyType;
  priority?: number;
}

// 事件发射器基类
export declare class EventEmitter {
  on(event: string, callback: (...args: any[]) => void): this;
  once(event: string, callback: (...args: any[]) => void): this;
  off(event: string, callback?: (...args: any[]) => void): this;
  emit(event: string, ...args: any[]): boolean;
  listenerCount(event?: string): number;
  eventNames(): string[];
  listeners(event: string): ((...args: any[]) => void)[];
  removeAllListeners(): this;
}

// 组件基类
export declare class Component extends EventEmitter {
  readonly type: string;
  entity: Entity | null;
  enabled: boolean;
  
  constructor(type: string);
  getType(): string;
  setEntity(entity: Entity): void;
  getEntity(): Entity | null;
  setEnabled(enabled: boolean): void;
  isEnabled(): boolean;
  onAttach(): void;
  onEnable(): void;
  onDisable(): void;
  update(deltaTime: number): void;
  fixedUpdate(fixedDeltaTime: number): void;
  dispose(): void;
}

// 实体类
export declare class Entity extends EventEmitter {
  readonly id: string;
  name: string;
  active: boolean;
  
  constructor(name?: string);
  addComponent<T extends Component>(component: T): T;
  removeComponent<T extends Component>(componentType: string): T | null;
  getComponent<T extends Component>(componentType: string): T | null;
  getComponents<T extends Component>(componentType: string): T[];
  hasComponent(componentType: string): boolean;
  setActive(active: boolean): void;
  isActive(): boolean;
  update(deltaTime: number): void;
  fixedUpdate(fixedDeltaTime: number): void;
  dispose(): void;
}

// 变换组件
export declare class Transform extends Component {
  static readonly type: string;
  
  setPosition(x: number | THREE.Vector3, y?: number, z?: number): void;
  getPosition(): THREE.Vector3;
  setWorldPosition(x: number | THREE.Vector3, y?: number, z?: number): void;
  getWorldPosition(): THREE.Vector3;
  setRotation(x: number | THREE.Euler, y?: number, z?: number, order?: string): void;
  getRotation(): THREE.Euler;
  setScale(x: number | THREE.Vector3, y?: number, z?: number): void;
  getScale(): THREE.Vector3;
  lookAt(target: THREE.Vector3, up?: THREE.Vector3): void;
  setParent(parent: Transform | null): void;
  getParent(): Transform | null;
  getChildren(): Transform[];
  getObject3D(): THREE.Object3D;
}

// 场景管理器
export declare class SceneManager extends EventEmitter {
  static getInstance(): SceneManager;
  createScene(name: string): Scene;
  getScene(name: string): Scene | null;
  setActiveScene(name: string): boolean;
  getActiveScene(): Scene | null;
  removeScene(name: string): boolean;
  getAllScenes(): Scene[];
}

// 场景类
export declare class Scene extends EventEmitter {
  readonly name: string;
  
  constructor(name: string);
  addEntity(entity: Entity): void;
  removeEntity(entity: Entity): void;
  getEntity(id: string): Entity | null;
  getEntities(): Entity[];
  update(deltaTime: number): void;
  dispose(): void;
}

// 资源管理器
export declare class ResourceManager extends EventEmitter {
  static getInstance(): ResourceManager;
  loadResource<T = any>(url: string, type?: AssetType): Promise<T>;
  getResource<T = any>(url: string): T | null;
  unloadResource(url: string): boolean;
  preloadResources(urls: string[]): Promise<void>;
  getLoadedResources(): Map<string, any>;
  clearCache(): void;
}

// 依赖管理器
export declare class DependencyManager extends EventEmitter {
  static getInstance(): DependencyManager;
  addDependency(resourceId: string, dependencyId: string, type?: DependencyType, priority?: number): void;
  removeDependency(resourceId: string, dependencyId: string): void;
  getDependencies(resourceId: string): DependencyInfo[];
  getDependents(resourceId: string): string[];
  resolveDependencies(resourceId: string): string[];
  checkCircularDependencies(): string[][];
}

// 引擎主类
export declare class Engine extends EventEmitter {
  static getInstance(): Engine;
  initialize(canvas: HTMLCanvasElement, options?: any): Promise<void>;
  start(): void;
  stop(): void;
  pause(): void;
  resume(): void;
  update(deltaTime: number): void;
  render(): void;
  dispose(): void;
  
  getSceneManager(): SceneManager;
  getResourceManager(): ResourceManager;
  getDependencyManager(): DependencyManager;
}

// 导出默认引擎实例
declare const engine: Engine;
export default engine;
