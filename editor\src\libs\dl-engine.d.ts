/**
 * DL Engine 类型声明文件
 * 为 dl-engine.mjs 提供 TypeScript 类型支持
 */

// 基础类型和枚举
export enum DependencyType {
  STRONG = 'strong',
  WEAK = 'weak',
  LAZY = 'lazy'
}

export enum AssetType {
  TEXTURE = 'texture',
  MODEL = 'model',
  MATERIAL = 'material',
  SHADER = 'shader',
  AUDIO = 'audio',
  SCENE = 'scene',
  ANIMATION = 'animation',
  SCRIPT = 'script',
  FONT = 'font',
  VIDEO = 'video',
  DATA = 'data'
}

export enum MaterialType {
  BASIC = 'basic',
  STANDARD = 'standard',
  PHYSICAL = 'physical',
  PHONG = 'phong',
  LAMBERT = 'lambert'
}

export enum LightType {
  DIRECTIONAL = 'directional',
  POINT = 'point',
  SPOT = 'spot',
  AMBIENT = 'ambient'
}

export enum CameraType {
  PERSPECTIVE = 'perspective',
  ORTHOGRAPHIC = 'orthographic'
}

export enum AnimationBlendMode {
  OVERRIDE = 'override',
  ADDITIVE = 'additive'
}

export enum AnimationLoopMode {
  ONCE = 'once',
  REPEAT = 'repeat',
  PING_PONG = 'pingpong'
}

export enum AnimationEventType {
  START = 'start',
  END = 'end',
  LOOP = 'loop'
}

export enum AudioEventType {
  PLAY = 'play',
  PAUSE = 'pause',
  STOP = 'stop',
  END = 'end'
}

export enum AudioSourceState {
  STOPPED = 'stopped',
  PLAYING = 'playing',
  PAUSED = 'paused'
}

export enum AudioSourceEventType {
  PLAY = 'play',
  PAUSE = 'pause',
  STOP = 'stop',
  VOLUME_CHANGE = 'volumeChange'
}

export enum AudioType {
  MUSIC = 'music',
  SFX = 'sfx',
  VOICE = 'voice',
  AMBIENT = 'ambient'
}

export enum NetworkEventType {
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  MESSAGE = 'message',
  ERROR = 'error'
}

export enum NetworkState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting'
}

export enum NetworkConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  FAILED = 'failed'
}

export enum NetworkEntityType {
  STATIC = 'static',
  DYNAMIC = 'dynamic',
  KINEMATIC = 'kinematic'
}

export enum NetworkEntitySyncMode {
  NONE = 'none',
  TRANSFORM = 'transform',
  FULL = 'full'
}

export enum NetworkEntityOwnershipMode {
  SERVER = 'server',
  CLIENT = 'client',
  SHARED = 'shared'
}

export enum NetworkUserRole {
  GUEST = 'guest',
  USER = 'user',
  MODERATOR = 'moderator',
  ADMIN = 'admin'
}

export enum NetworkUserState {
  OFFLINE = 'offline',
  ONLINE = 'online',
  AWAY = 'away',
  BUSY = 'busy'
}

export enum MessageType {
  TEXT = 'text',
  BINARY = 'binary',
  JSON = 'json'
}

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

export enum UILayoutType {
  NONE = 'none',
  GRID = 'grid',
  FLEX = 'flex',
  ABSOLUTE = 'absolute',
  RELATIVE = 'relative'
}

export enum UIEventType {
  CLICK = 'click',
  HOVER = 'hover',
  DRAG_START = 'dragstart',
  DRAG = 'drag',
  DRAG_END = 'dragend',
  FOCUS = 'focus',
  BLUR = 'blur',
  KEY_DOWN = 'keydown',
  KEY_UP = 'keyup'
}

export enum UIAnimationType {
  FADE = 'fade',
  SCALE = 'scale',
  MOVE = 'move',
  ROTATE = 'rotate',
  COLOR = 'color'
}

export enum UIComponentType {
  BASE = 'base',
  CONTAINER = 'container',
  BUTTON = 'button',
  TEXT = 'text',
  IMAGE = 'image',
  INPUT = 'input',
  SLIDER = 'slider',
  CHECKBOX = 'checkbox',
  DROPDOWN = 'dropdown',
  PANEL = 'panel',
  WINDOW = 'window',
  CUSTOM = 'custom'
}

// 依赖信息接口
export interface DependencyInfo {
  id: string;
  type: DependencyType;
  priority?: number;
}

// 输入相关枚举
export enum InputEventType {
  KEY_DOWN = 'keydown',
  KEY_UP = 'keyup',
  MOUSE_DOWN = 'mousedown',
  MOUSE_UP = 'mouseup',
  MOUSE_MOVE = 'mousemove',
  TOUCH_START = 'touchstart',
  TOUCH_END = 'touchend',
  TOUCH_MOVE = 'touchmove'
}

export enum InputActionType {
  BUTTON = 'button',
  AXIS = 'axis',
  VECTOR = 'vector'
}

export enum InputMappingType {
  BUTTON = 'button',
  AXIS = 'axis',
  COMPOSITE = 'composite',
  VECTOR = 'vector'
}

export enum KeyState {
  UP = 'up',
  DOWN = 'down',
  PRESSED = 'pressed',
  RELEASED = 'released'
}

export enum MouseButton {
  LEFT = 0,
  MIDDLE = 1,
  RIGHT = 2
}

export enum MouseButtonState {
  UP = 'up',
  DOWN = 'down',
  PRESSED = 'pressed',
  RELEASED = 'released'
}

// 物理相关枚举
export enum SoftBodyType {
  CLOTH = 'cloth',
  ROPE = 'rope',
  VOLUME = 'volume'
}

// 交互相关枚举
export enum InteractionType {
  HOVER = 'hover',
  CLICK = 'click',
  GRAB = 'grab',
  TOUCH = 'touch'
}

export enum InteractionEventType {
  START = 'start',
  UPDATE = 'update',
  END = 'end'
}

export enum GrabEventType {
  GRAB_START = 'grabStart',
  GRAB_UPDATE = 'grabUpdate',
  GRAB_END = 'grabEnd'
}

export enum GrabType {
  HAND = 'hand',
  CONTROLLER = 'controller',
  MOUSE = 'mouse'
}

export enum GrabState {
  IDLE = 'idle',
  HOVERING = 'hovering',
  GRABBING = 'grabbing'
}

export enum HighlightType {
  OUTLINE = 'outline',
  GLOW = 'glow',
  COLOR = 'color'
}

export enum PromptPositionType {
  WORLD = 'world',
  SCREEN = 'screen',
  ATTACHED = 'attached'
}

// 角色控制相关枚举
export enum CharacterMovementMode {
  WALKING = 'walking',
  RUNNING = 'running',
  FLYING = 'flying',
  SWIMMING = 'swimming'
}

export enum CharacterState {
  IDLE = 'idle',
  WALKING = 'walking',
  RUNNING = 'running',
  JUMPING = 'jumping',
  FALLING = 'falling',
  LANDING = 'landing'
}

export enum ControllerPresetType {
  BASIC = 'basic',
  ADVANCED = 'advanced',
  CUSTOM = 'custom'
}

// 压缩相关枚举
export enum CompressionLevel {
  NONE = 'none',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  MAXIMUM = 'maximum'
}

// 粒子系统相关枚举
export enum EmitterShapeType {
  POINT = 'point',
  BOX = 'box',
  SPHERE = 'sphere',
  CONE = 'cone',
  CIRCLE = 'circle'
}

// 场景相关枚举
export enum SceneTransitionType {
  FADE = 'fade',
  SLIDE = 'slide',
  DISSOLVE = 'dissolve',
  INSTANT = 'instant'
}

export enum SkyboxType {
  COLOR = 'color',
  GRADIENT = 'gradient',
  CUBEMAP = 'cubemap',
  HDRI = 'hdri'
}

// 资源相关枚举
export enum ResourceState {
  UNLOADED = 'unloaded',
  LOADING = 'loading',
  LOADED = 'loaded',
  ERROR = 'error'
}

// 碰撞检测相关枚举
export enum ContinuousCollisionDetection {
  DISABLED = 'disabled',
  ENABLED = 'enabled',
  ENHANCED = 'enhanced'
}

// 约束相关枚举
export enum ConstraintType {
  FIXED = 'fixed',
  HINGE = 'hinge',
  SLIDER = 'slider',
  WHEEL = 'wheel',
  SPRING = 'spring'
}

// 混合相关枚举
export enum CompositeBindingType {
  AND = 'and',
  OR = 'or',
  XOR = 'xor'
}

// 事件发射器基类
export declare class EventEmitter {
  on(event: string, callback: (...args: any[]) => void): this;
  once(event: string, callback: (...args: any[]) => void): this;
  off(event: string, callback?: (...args: any[]) => void): this;
  emit(event: string, ...args: any[]): boolean;
  listenerCount(event?: string): number;
  eventNames(): string[];
  listeners(event: string): ((...args: any[]) => void)[];
  removeAllListeners(): this;
}

// 系统基类
export declare class System extends EventEmitter {
  readonly priority: number;
  enabled: boolean;

  constructor(priority?: number);
  initialize(): void;
  update(deltaTime: number): void;
  fixedUpdate(fixedDeltaTime: number): void;
  dispose(): void;
  setEnabled(enabled: boolean): void;
  isEnabled(): boolean;
}

// 时间类
export declare class Time {
  static deltaTime: number;
  static fixedDeltaTime: number;
  static time: number;
  static timeScale: number;
  static frameCount: number;

  static getDeltaTime(): number;
  static getFixedDeltaTime(): number;
  static getTime(): number;
  static getTimeScale(): number;
  static setTimeScale(scale: number): void;
  static getFrameCount(): number;
}

// 调试类
export declare class Debug {
  static enabled: boolean;

  static log(category: string, message: string, ...args: any[]): void;
  static warn(category: string, message: string, ...args: any[]): void;
  static error(category: string, message: string, ...args: any[]): void;
  static setEnabled(enabled: boolean): void;
  static isEnabled(): boolean;
}

// 世界类
export declare class World extends EventEmitter {
  readonly systems: Map<string, System>;

  constructor();
  addSystem<T extends System>(system: T): T;
  removeSystem<T extends System>(systemType: string): T | null;
  getSystem<T extends System>(systemType: string): T | null;
  hasSystem(systemType: string): boolean;
  update(deltaTime: number): void;
  fixedUpdate(fixedDeltaTime: number): void;
  dispose(): void;
}

// 渲染器类
export declare class Renderer extends EventEmitter {
  readonly canvas: HTMLCanvasElement;
  readonly renderer: THREE.WebGLRenderer;

  constructor(canvas: HTMLCanvasElement, options?: any);
  render(scene: THREE.Scene, camera: THREE.Camera): void;
  setSize(width: number, height: number): void;
  getSize(): { width: number; height: number };
  dispose(): void;
}

// 引擎主类
export declare class Engine extends EventEmitter {
  static getInstance(): Engine;

  readonly world: World;
  readonly renderer: Renderer;
  readonly sceneManager: SceneManager;
  readonly resourceManager: ResourceManager;

  initialize(canvas: HTMLCanvasElement, options?: any): Promise<void>;
  start(): void;
  stop(): void;
  pause(): void;
  resume(): void;
  update(deltaTime: number): void;
  render(): void;
  dispose(): void;

  getWorld(): World;
  getRenderer(): Renderer;
  getSceneManager(): SceneManager;
  getResourceManager(): ResourceManager;
  getDependencyManager(): DependencyManager;
}

// 场景管理器
export declare class SceneManager extends EventEmitter {
  static getInstance(): SceneManager;

  createScene(name: string): Scene;
  getScene(name: string): Scene | null;
  setActiveScene(name: string): boolean;
  getActiveScene(): Scene | null;
  removeScene(name: string): boolean;
  getAllScenes(): Scene[];
  loadScene(url: string): Promise<Scene>;
  saveScene(scene: Scene, url: string): Promise<void>;
}

// 场景类
export declare class Scene extends EventEmitter {
  readonly name: string;
  readonly entities: Map<string, Entity>;
  readonly threeScene: THREE.Scene;

  constructor(name: string);
  addEntity(entity: Entity): void;
  removeEntity(entity: Entity): void;
  getEntity(id: string): Entity | null;
  getEntities(): Entity[];
  getThreeScene(): THREE.Scene;
  update(deltaTime: number): void;
  dispose(): void;
}

// 资源管理器
export declare class ResourceManager extends EventEmitter {
  static getInstance(): ResourceManager;

  loadResource<T = any>(url: string, type?: AssetType): Promise<T>;
  getResource<T = any>(url: string): T | null;
  unloadResource(url: string): boolean;
  preloadResources(urls: string[]): Promise<void>;
  getLoadedResources(): Map<string, any>;
  clearCache(): void;
  setBasePath(path: string): void;
  getBasePath(): string;
}

// 依赖管理器
export declare class DependencyManager extends EventEmitter {
  static getInstance(): DependencyManager;

  addDependency(resourceId: string, dependencyId: string, type?: DependencyType, priority?: number): void;
  removeDependency(resourceId: string, dependencyId: string): void;
  getDependencies(resourceId: string): DependencyInfo[];
  getDependents(resourceId: string): string[];
  resolveDependencies(resourceId: string): string[];
  checkCircularDependencies(): string[][];
}

// 组件基类
export declare class Component extends EventEmitter {
  readonly type: string;
  entity: Entity | null;
  enabled: boolean;

  constructor(type: string);
  getType(): string;
  setEntity(entity: Entity): void;
  getEntity(): Entity | null;
  setEnabled(enabled: boolean): void;
  isEnabled(): boolean;
  onAttach(): void;
  onEnable(): void;
  onDisable(): void;
  update(deltaTime: number): void;
  fixedUpdate(fixedDeltaTime: number): void;
  dispose(): void;
}

// 实体类
export declare class Entity extends EventEmitter {
  readonly id: string;
  name: string;
  active: boolean;

  constructor(name?: string);
  addComponent<T extends Component>(component: T): T;
  removeComponent<T extends Component>(componentType: string): T | null;
  getComponent<T extends Component>(componentType: string): T | null;
  getComponents<T extends Component>(componentType: string): T[];
  hasComponent(componentType: string): boolean;
  setActive(active: boolean): void;
  isActive(): boolean;
  update(deltaTime: number): void;
  fixedUpdate(fixedDeltaTime: number): void;
  dispose(): void;
}

// 变换组件
export declare class Transform extends Component {
  static readonly type: string;

  setPosition(x: number | THREE.Vector3, y?: number, z?: number): void;
  getPosition(): THREE.Vector3;
  setWorldPosition(x: number | THREE.Vector3, y?: number, z?: number): void;
  getWorldPosition(): THREE.Vector3;
  setRotation(x: number | THREE.Euler, y?: number, z?: number, order?: string): void;
  getRotation(): THREE.Euler;
  setScale(x: number | THREE.Vector3, y?: number, z?: number): void;
  getScale(): THREE.Vector3;
  lookAt(target: THREE.Vector3, up?: THREE.Vector3): void;
  setParent(parent: Transform | null): void;
  getParent(): Transform | null;
  getChildren(): Transform[];
  getObject3D(): THREE.Object3D;
}

// 相机组件
export declare class Camera extends Component {
  static readonly type: string;

  readonly camera: THREE.Camera;
  cameraType: CameraType;

  constructor(type?: CameraType);
  getCamera(): THREE.Camera;
  setCameraType(type: CameraType): void;
  getCameraType(): CameraType;
  setFOV(fov: number): void;
  getFOV(): number;
  setNear(near: number): void;
  getNear(): number;
  setFar(far: number): void;
  getFar(): number;
  setAspect(aspect: number): void;
  getAspect(): number;
}

// 光源组件
export declare class Light extends Component {
  static readonly type: string;

  readonly light: THREE.Light;
  lightType: LightType;

  constructor(type?: LightType);
  getLight(): THREE.Light;
  setLightType(type: LightType): void;
  getLightType(): LightType;
  setColor(color: THREE.Color | string | number): void;
  getColor(): THREE.Color;
  setIntensity(intensity: number): void;
  getIntensity(): number;
}

// 动画器组件
export declare class Animator extends Component {
  static readonly type: string;

  readonly mixer: THREE.AnimationMixer;
  readonly clips: Map<string, THREE.AnimationClip>;
  readonly actions: Map<string, THREE.AnimationAction>;

  constructor();
  addClip(name: string, clip: THREE.AnimationClip): void;
  removeClip(name: string): boolean;
  getClip(name: string): THREE.AnimationClip | null;
  play(clipName: string, fadeTime?: number): THREE.AnimationAction | null;
  stop(clipName: string, fadeTime?: number): void;
  pause(clipName: string): void;
  resume(clipName: string): void;
  setWeight(clipName: string, weight: number): void;
  getWeight(clipName: string): number;
  setLoop(clipName: string, loop: AnimationLoopMode): void;
  getLoop(clipName: string): AnimationLoopMode;
  setSpeed(clipName: string, speed: number): void;
  getSpeed(clipName: string): number;
  getTime(): number;
  update(deltaTime: number): void;
}

// 音频监听器组件
export declare class AudioListener extends Component {
  static readonly type: string;

  readonly listener: THREE.AudioListener;

  constructor();
  getListener(): THREE.AudioListener;
  setMasterVolume(volume: number): void;
  getMasterVolume(): number;
}

// 音频源组件
export declare class AudioSource extends Component {
  static readonly type: string;

  readonly audio: THREE.Audio | THREE.PositionalAudio;
  audioType: AudioType;
  state: AudioSourceState;

  constructor(type?: AudioType);
  loadAudio(url: string): Promise<void>;
  play(): void;
  pause(): void;
  stop(): void;
  setVolume(volume: number): void;
  getVolume(): number;
  setLoop(loop: boolean): void;
  getLoop(): boolean;
  setPlaybackRate(rate: number): void;
  getPlaybackRate(): number;
  getDuration(): number;
  getCurrentTime(): number;
  setCurrentTime(time: number): void;
  getState(): AudioSourceState;
}

// 场景管理器
export declare class SceneManager extends EventEmitter {
  static getInstance(): SceneManager;
  createScene(name: string): Scene;
  getScene(name: string): Scene | null;
  setActiveScene(name: string): boolean;
  getActiveScene(): Scene | null;
  removeScene(name: string): boolean;
  getAllScenes(): Scene[];
}

// 场景类
export declare class Scene extends EventEmitter {
  readonly name: string;
  
  constructor(name: string);
  addEntity(entity: Entity): void;
  removeEntity(entity: Entity): void;
  getEntity(id: string): Entity | null;
  getEntities(): Entity[];
  update(deltaTime: number): void;
  dispose(): void;
}

// 资源管理器
export declare class ResourceManager extends EventEmitter {
  static getInstance(): ResourceManager;
  loadResource<T = any>(url: string, type?: AssetType): Promise<T>;
  getResource<T = any>(url: string): T | null;
  unloadResource(url: string): boolean;
  preloadResources(urls: string[]): Promise<void>;
  getLoadedResources(): Map<string, any>;
  clearCache(): void;
}

// 依赖管理器
export declare class DependencyManager extends EventEmitter {
  static getInstance(): DependencyManager;
  addDependency(resourceId: string, dependencyId: string, type?: DependencyType, priority?: number): void;
  removeDependency(resourceId: string, dependencyId: string): void;
  getDependencies(resourceId: string): DependencyInfo[];
  getDependents(resourceId: string): string[];
  resolveDependencies(resourceId: string): string[];
  checkCircularDependencies(): string[][];
}

// 引擎主类
export declare class Engine extends EventEmitter {
  static getInstance(): Engine;
  initialize(canvas: HTMLCanvasElement, options?: any): Promise<void>;
  start(): void;
  stop(): void;
  pause(): void;
  resume(): void;
  update(deltaTime: number): void;
  render(): void;
  dispose(): void;
  
  getSceneManager(): SceneManager;
  getResourceManager(): ResourceManager;
  getDependencyManager(): DependencyManager;
}

// 动画状态机相关类型
export interface AnimationStateMachineData {
  states: AnimationStateData[];
  transitions: TransitionData[];
  parameters: ParameterData[];
  defaultState?: string;
}

export interface AnimationStateData {
  name: string;
  type: 'SingleAnimationState' | 'BlendAnimationState';
  clipName?: string;
  loop?: boolean;
  parameterName?: string;
  blendSpaceType?: '1D' | '2D';
}

export interface TransitionData {
  from: string;
  to: string;
  duration: number;
  condition: () => boolean;
}

export interface ParameterData {
  name: string;
  type: 'float' | 'int' | 'bool' | 'trigger';
  defaultValue: any;
  metadata?: any;
}

// 动画状态机类
export declare class AnimationStateMachine {
  constructor(animator: Animator);
  addState(state: AnimationStateData): void;
  addTransition(rule: TransitionData): void;
  setParameter(name: string, value: any): void;
  getParameter(name: string): any;
  setCurrentState(stateName: string): void;
  update(deltaTime: number): void;
  getCurrentState(): AnimationStateData | null;
  getPreviousState(): AnimationStateData | null;
  getStates(): AnimationStateData[];
  getTransitions(): TransitionData[];
  getParameters(): Map<string, any>;
  reset(): void;
  clear(): void;
}

// 动画状态类
export declare class AnimationState {
  readonly name: string;
  readonly type: string;

  constructor(name: string, type: string);
  getName(): string;
  getType(): string;
}

// 系统类声明
export declare class AnimationSystem extends System {
  static readonly type: string;
  constructor();
}

export declare class AudioSystem extends System {
  static readonly type: string;
  constructor();
}

export declare class PhysicsSystem extends System {
  static readonly type: string;
  constructor();
}

export declare class RenderSystem extends System {
  static readonly type: string;
  constructor();
}

export declare class InputSystem extends System {
  static readonly type: string;
  constructor();
}

export declare class NetworkSystem extends System {
  static readonly type: string;
  constructor();
}

export declare class UISystem extends System {
  static readonly type: string;
  constructor();
}

// 工具函数
export declare function generateUUID(): string;
export declare function generateShortId(): string;
export declare function isValidUUID(uuid: string): boolean;
export declare function registerComponent<T extends Component>(componentClass: new (...args: any[]) => T): void;
export declare function getComponentClass<T extends Component>(type: string): (new (...args: any[]) => T) | null;

// 导出默认引擎实例
declare const engine: Engine;
export default engine;
