/**
 * 材质编辑器组件
 */
import React, { useState, useEffect, useRef } from 'react';
import { Tabs, Form, Input, InputNumber, Select, Button, Collapse, Space, Slider, Upload, message } from 'antd';
import { UploadOutlined, PlusOutlined, DeleteOutlined, CopyOutlined, SaveOutlined } from '@ant-design/icons';
import { SketchPicker } from 'react-color';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { Engine, Material, MaterialType, Texture } from 'dl-engine-core';
import './MaterialEditor.less';

const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Option } = Select;

// 材质类型选项
const materialTypeOptions = [
  { value: MaterialType.STANDARD, label: 'Standard' },
  { value: MaterialType.PHYSICAL, label: 'Physical (PBR)' },
  { value: MaterialType.BASIC, label: 'Basic' },
  { value: MaterialType.LAMBERT, label: 'Lambert' },
  { value: MaterialType.PHONG, label: 'Phong' },
  { value: MaterialType.TOON, label: 'Toon' },
];

// 纹理类型选项
const textureTypeOptions = [
  { value: 'map', label: '颜色贴图' },
  { value: 'normalMap', label: '法线贴图' },
  { value: 'bumpMap', label: '凹凸贴图' },
  { value: 'roughnessMap', label: '粗糙度贴图' },
  { value: 'metalnessMap', label: '金属度贴图' },
  { value: 'aoMap', label: '环境光遮蔽贴图' },
  { value: 'emissiveMap', label: '自发光贴图' },
  { value: 'displacementMap', label: '位移贴图' },
  { value: 'envMap', label: '环境贴图' },
  { value: 'lightMap', label: '光照贴图' },
];

interface MaterialEditorProps {
  materialId?: string;
  onSave?: (material: any) => void;
  onCancel?: () => void;
}

const MaterialEditor: React.FC<MaterialEditorProps> = ({ materialId, onSave, onCancel }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);
  const engineRef = useRef<any>(null);
  
  // 材质状态
  const [materialType, setMaterialType] = useState<MaterialType>(MaterialType.STANDARD);
  const [colorPickerVisible, setColorPickerVisible] = useState<{ [key: string]: boolean }>({
    color: false,
    emissive: false,
  });
  const [previewMode, setPreviewMode] = useState<'sphere' | 'cube' | 'plane'>('sphere');
  
  // 从Redux获取材质数据
  const material = useSelector((state: RootState) => {
    if (!materialId) return null;
    // 这里应该从Redux状态中获取材质数据
    // 由于我们没有实际的Redux状态，这里只是一个示例
    return null;
  });
  
  // 初始化引擎和预览
  useEffect(() => {
    if (!previewCanvasRef.current) return;
    
    // 创建引擎实例
    engineRef.current = new Engine({
      canvas: previewCanvasRef.current,
      width: previewCanvasRef.current.clientWidth,
      height: previewCanvasRef.current.clientHeight,
    });
    
    // 设置预览场景
    setupPreviewScene();
    
    // 如果有材质ID，加载材质
    if (materialId) {
      loadMaterial(materialId);
    }
    
    // 清理函数
    return () => {
      if (engineRef.current) {
        engineRef.current.dispose();
      }
    };
  }, [materialId]);
  
  // 设置预览场景
  const setupPreviewScene = () => {
    if (!engineRef.current) return;
    
    // 创建场景、相机和灯光
    const scene = engineRef.current.createScene();
    const camera = engineRef.current.createCamera({
      type: 'perspective',
      position: [0, 0, 5],
      lookAt: [0, 0, 0],
    });
    
    // 添加环境光和方向光
    engineRef.current.createLight({
      type: 'ambient',
      intensity: 0.5,
    });
    
    engineRef.current.createLight({
      type: 'directional',
      intensity: 0.8,
      position: [1, 1, 1],
      castShadow: true,
    });
    
    // 创建预览对象
    createPreviewObject(previewMode);
    
    // 启动引擎
    engineRef.current.start();
  };
  
  // 创建预览对象
  const createPreviewObject = (mode: 'sphere' | 'cube' | 'plane') => {
    if (!engineRef.current) return;
    
    // 移除现有的预览对象
    engineRef.current.getScene().removeAllEntities();
    
    // 创建新的预览对象
    let geometry;
    switch (mode) {
      case 'sphere':
        geometry = engineRef.current.createGeometry({
          type: 'sphere',
          radius: 1,
          widthSegments: 32,
          heightSegments: 32,
        });
        break;
      case 'cube':
        geometry = engineRef.current.createGeometry({
          type: 'box',
          width: 1.5,
          height: 1.5,
          depth: 1.5,
        });
        break;
      case 'plane':
        geometry = engineRef.current.createGeometry({
          type: 'plane',
          width: 2,
          height: 2,
        });
        break;
    }
    
    // 创建材质
    const material = engineRef.current.createMaterial({
      type: materialType,
      color: form.getFieldValue('color') || '#ffffff',
      metalness: form.getFieldValue('metalness') || 0,
      roughness: form.getFieldValue('roughness') || 0.5,
      emissive: form.getFieldValue('emissive') || '#000000',
      emissiveIntensity: form.getFieldValue('emissiveIntensity') || 0,
    });
    
    // 创建网格
    engineRef.current.createMesh({
      geometry,
      material,
    });
  };
  
  // 加载材质
  const loadMaterial = (id: string) => {
    // 这里应该从API或Redux加载材质数据
    // 由于我们没有实际的API，这里只是一个示例
    const dummyMaterial = {
      id,
      name: '示例材质',
      type: MaterialType.STANDARD,
      color: '#ff0000',
      metalness: 0.5,
      roughness: 0.5,
      emissive: '#000000',
      emissiveIntensity: 0,
      transparent: false,
      opacity: 1,
      side: 'front',
      textures: [],
    };
    
    // 设置表单值
    form.setFieldsValue(dummyMaterial);
    setMaterialType(dummyMaterial.type);
    
    // 更新预览
    updatePreview(dummyMaterial);
  };
  
  // 更新预览
  const updatePreview = (materialData: any) => {
    if (!engineRef.current) return;
    
    // 获取预览对象
    const mesh = engineRef.current.getScene().getEntities()[0];
    if (!mesh) return;
    
    // 更新材质
    const material = mesh.getComponent('material');
    if (material) {
      Object.keys(materialData).forEach(key => {
        if (key !== 'id' && key !== 'name' && key !== 'type' && key !== 'textures') {
          material.setProperty(key, materialData[key]);
        }
      });
    }
  };
  
  // 处理表单值变化
  const handleValuesChange = (changedValues: any, allValues: any) => {
    // 如果材质类型变化，更新预览对象
    if (changedValues.type && changedValues.type !== materialType) {
      setMaterialType(changedValues.type);
      createPreviewObject(previewMode);
    } else {
      // 否则只更新材质属性
      updatePreview(allValues);
    }
  };
  
  // 处理保存
  const handleSave = () => {
    form.validateFields().then(values => {
      if (onSave) {
        onSave({
          id: materialId,
          ...values,
        });
      }
    });
  };
  
  // 处理颜色变化
  const handleColorChange = (colorType: string, color: any) => {
    form.setFieldValue(colorType, color.hex);
    updatePreview({ [colorType]: color.hex });
  };
  
  // 处理预览模式变化
  const handlePreviewModeChange = (mode: 'sphere' | 'cube' | 'plane') => {
    setPreviewMode(mode);
    createPreviewObject(mode);
  };
  
  return (
    <div className="material-editor">
      <div className="material-editor-header">
        <h2>{materialId ? t('editor.material.edit') : t('editor.material.create')}</h2>
        <div className="material-editor-actions">
          <Button onClick={onCancel}>{t('editor.cancel')}</Button>
          <Button type="primary" icon={<SaveOutlined />} onClick={handleSave}>
            {t('editor.save')}
          </Button>
        </div>
      </div>
      
      <div className="material-editor-content">
        <div className="material-editor-preview">
          <canvas ref={previewCanvasRef} className="preview-canvas" />
          <div className="preview-controls">
            <Button
              type={previewMode === 'sphere' ? 'primary' : 'default'}
              onClick={() => handlePreviewModeChange('sphere')}
            >
              {t('editor.material.sphere')}
            </Button>
            <Button
              type={previewMode === 'cube' ? 'primary' : 'default'}
              onClick={() => handlePreviewModeChange('cube')}
            >
              {t('editor.material.cube')}
            </Button>
            <Button
              type={previewMode === 'plane' ? 'primary' : 'default'}
              onClick={() => handlePreviewModeChange('plane')}
            >
              {t('editor.material.plane')}
            </Button>
          </div>
        </div>
        
        <div className="material-editor-form">
          <Form
            form={form}
            layout="vertical"
            onValuesChange={handleValuesChange}
          >
            <Form.Item name="name" label={t('editor.material.name')} rules={[{ required: true }]}>
              <Input />
            </Form.Item>
            
            <Form.Item name="type" label={t('editor.material.type')} rules={[{ required: true }]}>
              <Select>
                {materialTypeOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            
            <Tabs defaultActiveKey="basic">
              <TabPane tab={t('editor.material.basicProps')} key="basic">
                <Form.Item name="color" label={t('editor.material.color')}>
                  <div className="color-picker-field">
                    <div
                      className="color-preview"
                      style={{ backgroundColor: form.getFieldValue('color') || '#ffffff' }}
                      onClick={() => setColorPickerVisible({ ...colorPickerVisible, color: !colorPickerVisible.color })}
                    />
                    <Input
                      value={form.getFieldValue('color')}
                      onChange={e => {
                        form.setFieldValue('color', e.target.value);
                        updatePreview({ color: e.target.value });
                      }}
                    />
                    {colorPickerVisible.color && (
                      <div className="color-picker-popover">
                        <div
                          className="color-picker-cover"
                          onClick={() => setColorPickerVisible({ ...colorPickerVisible, color: false })}
                        />
                        <SketchPicker
                          color={form.getFieldValue('color') || '#ffffff'}
                          onChange={color => handleColorChange('color', color)}
                        />
                      </div>
                    )}
                  </div>
                </Form.Item>
                
                {(materialType === MaterialType.STANDARD || materialType === MaterialType.PHYSICAL) && (
                  <>
                    <Form.Item name="metalness" label={t('editor.material.metalness')}>
                      <Slider min={0} max={1} step={0.01} />
                    </Form.Item>
                    
                    <Form.Item name="roughness" label={t('editor.material.roughness')}>
                      <Slider min={0} max={1} step={0.01} />
                    </Form.Item>
                  </>
                )}
                
                <Form.Item name="emissive" label={t('editor.material.emissive')}>
                  <div className="color-picker-field">
                    <div
                      className="color-preview"
                      style={{ backgroundColor: form.getFieldValue('emissive') || '#000000' }}
                      onClick={() => setColorPickerVisible({ ...colorPickerVisible, emissive: !colorPickerVisible.emissive })}
                    />
                    <Input
                      value={form.getFieldValue('emissive')}
                      onChange={e => {
                        form.setFieldValue('emissive', e.target.value);
                        updatePreview({ emissive: e.target.value });
                      }}
                    />
                    {colorPickerVisible.emissive && (
                      <div className="color-picker-popover">
                        <div
                          className="color-picker-cover"
                          onClick={() => setColorPickerVisible({ ...colorPickerVisible, emissive: false })}
                        />
                        <SketchPicker
                          color={form.getFieldValue('emissive') || '#000000'}
                          onChange={color => handleColorChange('emissive', color)}
                        />
                      </div>
                    )}
                  </div>
                </Form.Item>
                
                <Form.Item name="emissiveIntensity" label={t('editor.material.emissiveIntensity')}>
                  <Slider min={0} max={10} step={0.1} />
                </Form.Item>
                
                <Form.Item name="transparent" label={t('editor.material.transparent')} valuePropName="checked">
                  <Select>
                    <Option value={false}>{t('editor.no')}</Option>
                    <Option value={true}>{t('editor.yes')}</Option>
                  </Select>
                </Form.Item>
                
                <Form.Item name="opacity" label={t('editor.material.opacity')}>
                  <Slider min={0} max={1} step={0.01} disabled={!form.getFieldValue('transparent')} />
                </Form.Item>
                
                <Form.Item name="side" label={t('editor.material.side')}>
                  <Select>
                    <Option value="front">{t('editor.material.frontSide')}</Option>
                    <Option value="back">{t('editor.material.backSide')}</Option>
                    <Option value="double">{t('editor.material.doubleSide')}</Option>
                  </Select>
                </Form.Item>
              </TabPane>
              
              <TabPane tab={t('editor.material.textures')} key="textures">
                <Form.List name="textures">
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map(field => (
                        <div key={field.key} className="texture-item">
                          <Form.Item
                            {...field}
                            name={[field.name, 'type']}
                            fieldKey={[field.fieldKey, 'type']}
                            label={t('editor.material.textureType')}
                            rules={[{ required: true }]}
                          >
                            <Select>
                              {textureTypeOptions.map(option => (
                                <Option key={option.value} value={option.value}>
                                  {option.label}
                                </Option>
                              ))}
                            </Select>
                          </Form.Item>
                          
                          <Form.Item
                            {...field}
                            name={[field.name, 'url']}
                            fieldKey={[field.fieldKey, 'url']}
                            label={t('editor.material.textureUrl')}
                            rules={[{ required: true }]}
                          >
                            <Input />
                          </Form.Item>
                          
                          <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() => remove(field.name)}
                          />
                        </div>
                      ))}
                      
                      <Form.Item>
                        <Button
                          type="dashed"
                          onClick={() => add()}
                          block
                          icon={<PlusOutlined />}
                        >
                          {t('editor.material.addTexture')}
                        </Button>
                      </Form.Item>
                    </>
                  )}
                </Form.List>
              </TabPane>
            </Tabs>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default MaterialEditor;
