/**
 * 性能对比服务
 * 用于保存和比较性能数据
 */
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 性能指标类型
 */
export enum PerformanceMetricType {
  /** 帧率 */
  FPS = 'fps',
  /** 渲染时间 */
  RENDER_TIME = 'renderTime',
  /** 物理更新时间 */
  PHYSICS_TIME = 'physicsTime',
  /** 动画更新时间 */
  ANIMATION_TIME = 'animationTime',
  /** 输入处理时间 */
  INPUT_TIME = 'inputTime',
  /** 网络更新时间 */
  NETWORK_TIME = 'networkTime',
  /** 脚本执行时间 */
  SCRIPT_TIME = 'scriptTime',
  /** 总更新时间 */
  TOTAL_UPDATE_TIME = 'totalUpdateTime',
  /** 内存使用 */
  MEMORY_USAGE = 'memoryUsage',
  /** 纹理内存 */
  TEXTURE_MEMORY = 'textureMemory',
  /** 几何体内存 */
  GEOMETRY_MEMORY = 'geometryMemory',
  /** 绘制调用次数 */
  DRAW_CALLS = 'drawCalls',
  /** 三角形数量 */
  TRIANGLES = 'triangles',
  /** 顶点数量 */
  VERTICES = 'vertices',
  /** 碰撞对数量 */
  COLLISION_PAIRS = 'collisionPairs',
  /** 接触点数量 */
  CONTACT_POINTS = 'contactPoints',
  /** 实体数量 */
  ENTITY_COUNT = 'entityCount',
  /** 组件数量 */
  COMPONENT_COUNT = 'componentCount',
  /** 系统数量 */
  SYSTEM_COUNT = 'systemCount',
  /** GPU使用率 */
  GPU_USAGE = 'gpuUsage',
  /** GPU内存使用 */
  GPU_MEMORY = 'gpuMemory',
  /** CPU使用率 */
  CPU_USAGE = 'cpuUsage',
  /** 网络延迟 */
  NETWORK_LATENCY = 'networkLatency',
  /** 资源加载时间 */
  RESOURCE_LOAD_TIME = 'resourceLoadTime',
  /** 资源数量 */
  RESOURCE_COUNT = 'resourceCount',
  /** 资源内存使用 */
  RESOURCE_MEMORY = 'resourceMemory',
}

/**
 * 性能指标
 */
export interface PerformanceMetric {
  /** 指标类型 */
  type: PerformanceMetricType;
  /** 指标名称 */
  name: string;
  /** 当前值 */
  value: number;
  /** 最小值 */
  min: number;
  /** 最大值 */
  max: number;
  /** 平均值 */
  average: number;
  /** 历史值 */
  history: number[];
  /** 历史长度限制 */
  historyLimit: number;
  /** 单位 */
  unit?: string;
  /** 阈值 */
  threshold?: number;
  /** 是否超过阈值 */
  exceedsThreshold?: boolean;
}

/**
 * 性能瓶颈
 */
export interface PerformanceBottleneck {
  /** 瓶颈类型 */
  type: string;
  /** 严重程度 (0-1) */
  severity: number;
  /** 描述 */
  description: string;
  /** 优化建议 */
  optimizationSuggestions?: string[];
}

/**
 * 性能趋势
 */
export interface PerformanceTrend {
  /** 指标类型 */
  metricType: PerformanceMetricType;
  /** 趋势类型 */
  type: 'improving' | 'stable' | 'degrading';
  /** 变化率 */
  changeRate: number;
  /** 置信度 */
  confidence: number;
}

/**
 * 性能报告
 */
export interface PerformanceReport {
  /** 报告时间 */
  timestamp: number;
  /** 性能指标 */
  metrics: { [key: string]: PerformanceMetric };
  /** 性能瓶颈 */
  bottlenecks: PerformanceBottleneck[];
  /** 性能趋势 */
  trends: PerformanceTrend[];
  /** 总体性能评分 (0-100) */
  overallScore: number;
  /** 性能状态 */
  status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  /** 自定义数据 */
  customData?: { [key: string]: any };
}

/**
 * 性能快照
 */
export interface PerformanceSnapshot {
  /** 快照ID */
  id: string;
  /** 快照名称 */
  name: string;
  /** 快照描述 */
  description?: string;
  /** 快照时间 */
  timestamp: number;
  /** 性能报告 */
  report: PerformanceReport;
  /** 标签 */
  tags?: string[];
}

/**
 * 性能对比结果
 */
export interface PerformanceComparisonResult {
  /** 基准快照 */
  baseSnapshot: PerformanceSnapshot;
  /** 比较快照 */
  compareSnapshot: PerformanceSnapshot;
  /** 指标变化 */
  metricChanges: {
    [key in PerformanceMetricType]?: {
      /** 基准值 */
      baseValue: number;
      /** 比较值 */
      compareValue: number;
      /** 绝对变化 */
      absoluteChange: number;
      /** 百分比变化 */
      percentChange: number;
      /** 是否改善 */
      isImprovement: boolean;
    };
  };
  /** 瓶颈变化 */
  bottleneckChanges: {
    /** 解决的瓶颈 */
    resolved: string[];
    /** 新增的瓶颈 */
    added: string[];
    /** 严重程度变化的瓶颈 */
    changed: {
      type: string;
      baseSeverity: number;
      compareSeverity: number;
      change: number;
    }[];
  };
  /** 总体评分变化 */
  scoreChange: {
    baseScore: number;
    compareScore: number;
    absoluteChange: number;
    percentChange: number;
  };
}

/**
 * 性能对比服务事件类型
 */
export enum PerformanceComparisonEventType {
  /** 快照创建 */
  SNAPSHOT_CREATED = 'snapshotCreated',
  /** 快照删除 */
  SNAPSHOT_DELETED = 'snapshotDeleted',
  /** 快照更新 */
  SNAPSHOT_UPDATED = 'snapshotUpdated',
  /** 比较完成 */
  COMPARISON_COMPLETED = 'comparisonCompleted',
}

/**
 * 性能对比服务
 */
export class PerformanceComparisonService {
  /** 单例实例 */
  private static instance: PerformanceComparisonService;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 性能快照 */
  private snapshots: Map<string, PerformanceSnapshot> = new Map();

  /** 最近的比较结果 */
  private lastComparisonResult: PerformanceComparisonResult | null = null;

  /**
   * 私有构造函数
   */
  private constructor() {
    // 从本地存储加载快照
    this.loadSnapshotsFromStorage();
  }

  /**
   * 获取单例实例
   * @returns 性能对比服务实例
   */
  public static getInstance(): PerformanceComparisonService {
    if (!PerformanceComparisonService.instance) {
      PerformanceComparisonService.instance = new PerformanceComparisonService();
    }
    return PerformanceComparisonService.instance;
  }

  /**
   * 创建性能快照
   * @param name 快照名称
   * @param report 性能报告
   * @param description 快照描述
   * @param tags 标签
   * @returns 创建的快照
   */
  public createSnapshot(name: string, report: PerformanceReport, description?: string, tags?: string[]): PerformanceSnapshot {
    const id = `snapshot_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
    const snapshot: PerformanceSnapshot = {
      id,
      name,
      description,
      timestamp: Date.now(),
      report,
      tags,
    };

    this.snapshots.set(id, snapshot);
    this.saveSnapshotsToStorage();
    this.eventEmitter.emit(PerformanceComparisonEventType.SNAPSHOT_CREATED, snapshot);

    return snapshot;
  }

  /**
   * 获取所有快照
   * @returns 快照数组
   */
  public getAllSnapshots(): PerformanceSnapshot[] {
    return Array.from(this.snapshots.values()).sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * 获取快照
   * @param id 快照ID
   * @returns 快照或undefined
   */
  public getSnapshot(id: string): PerformanceSnapshot | undefined {
    return this.snapshots.get(id);
  }

  /**
   * 删除快照
   * @param id 快照ID
   * @returns 是否成功删除
   */
  public deleteSnapshot(id: string): boolean {
    const deleted = this.snapshots.delete(id);
    if (deleted) {
      this.saveSnapshotsToStorage();
      this.eventEmitter.emit(PerformanceComparisonEventType.SNAPSHOT_DELETED, id);
    }
    return deleted;
  }

  /**
   * 更新快照
   * @param id 快照ID
   * @param updates 更新内容
   * @returns 更新后的快照或undefined
   */
  public updateSnapshot(id: string, updates: Partial<Omit<PerformanceSnapshot, 'id' | 'timestamp' | 'report'>>): PerformanceSnapshot | undefined {
    const snapshot = this.snapshots.get(id);
    if (!snapshot) {
      return undefined;
    }

    const updatedSnapshot = {
      ...snapshot,
      ...updates,
    };

    this.snapshots.set(id, updatedSnapshot);
    this.saveSnapshotsToStorage();
    this.eventEmitter.emit(PerformanceComparisonEventType.SNAPSHOT_UPDATED, updatedSnapshot);

    return updatedSnapshot;
  }

  /**
   * 比较两个快照
   * @param baseSnapshotId 基准快照ID
   * @param compareSnapshotId 比较快照ID
   * @returns 比较结果
   */
  public compareSnapshots(baseSnapshotId: string, compareSnapshotId: string): PerformanceComparisonResult | null {
    const baseSnapshot = this.snapshots.get(baseSnapshotId);
    const compareSnapshot = this.snapshots.get(compareSnapshotId);

    if (!baseSnapshot || !compareSnapshot) {
      return null;
    }

    const result = this.generateComparisonResult(baseSnapshot, compareSnapshot);
    this.lastComparisonResult = result;
    this.eventEmitter.emit(PerformanceComparisonEventType.COMPARISON_COMPLETED, result);

    return result;
  }

  /**
   * 获取最近的比较结果
   * @returns 最近的比较结果
   */
  public getLastComparisonResult(): PerformanceComparisonResult | null {
    return this.lastComparisonResult;
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public on(event: PerformanceComparisonEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public off(event: PerformanceComparisonEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 从本地存储加载快照
   * @private
   */
  private loadSnapshotsFromStorage(): void {
    try {
      const storedSnapshots = localStorage.getItem('performanceSnapshots');
      if (storedSnapshots) {
        const parsedSnapshots = JSON.parse(storedSnapshots) as PerformanceSnapshot[];
        parsedSnapshots.forEach(snapshot => {
          this.snapshots.set(snapshot.id, snapshot);
        });
      }
    } catch (error) {
      console.error('加载性能快照失败:', error);
    }
  }

  /**
   * 保存快照到本地存储
   * @private
   */
  private saveSnapshotsToStorage(): void {
    try {
      const snapshotsArray = Array.from(this.snapshots.values());
      localStorage.setItem('performanceSnapshots', JSON.stringify(snapshotsArray));
    } catch (error) {
      console.error('保存性能快照失败:', error);
    }
  }

  /**
   * 生成比较结果
   * @param baseSnapshot 基准快照
   * @param compareSnapshot 比较快照
   * @returns 比较结果
   * @private
   */
  private generateComparisonResult(baseSnapshot: PerformanceSnapshot, compareSnapshot: PerformanceSnapshot): PerformanceComparisonResult {
    const metricChanges: PerformanceComparisonResult['metricChanges'] = {};
    const bottleneckChanges: PerformanceComparisonResult['bottleneckChanges'] = {
      resolved: [],
      added: [],
      changed: [],
    };

    // 计算指标变化
    Object.keys(baseSnapshot.report.metrics).forEach(metricKey => {
      const key = metricKey as PerformanceMetricType;
      const baseMetric = baseSnapshot.report.metrics[key];
      const compareMetric = compareSnapshot.report.metrics[key];

      if (baseMetric && compareMetric) {
        const baseValue = baseMetric.value;
        const compareValue = compareMetric.value;
        const absoluteChange = compareValue - baseValue;
        const percentChange = baseValue !== 0 ? (absoluteChange / baseValue) * 100 : 0;

        // 判断是否为改善（根据指标类型，有些指标是越高越好，有些是越低越好）
        let isImprovement = false;
        switch (key) {
          case PerformanceMetricType.FPS:
            isImprovement = absoluteChange > 0;
            break;
          default:
            isImprovement = absoluteChange < 0;
            break;
        }

        metricChanges[key] = {
          baseValue,
          compareValue,
          absoluteChange,
          percentChange,
          isImprovement,
        };
      }
    });

    // 计算瓶颈变化
    const baseBottlenecks = baseSnapshot.report.bottlenecks.map(b => b.type);
    const compareBottlenecks = compareSnapshot.report.bottlenecks.map(b => b.type);

    // 解决的瓶颈
    bottleneckChanges.resolved = baseBottlenecks.filter(type => !compareBottlenecks.includes(type));

    // 新增的瓶颈
    bottleneckChanges.added = compareBottlenecks.filter(type => !baseBottlenecks.includes(type));

    // 严重程度变化的瓶颈
    baseSnapshot.report.bottlenecks.forEach(baseBottleneck => {
      const compareBottleneck = compareSnapshot.report.bottlenecks.find(b => b.type === baseBottleneck.type);
      if (compareBottleneck) {
        const change = compareBottleneck.severity - baseBottleneck.severity;
        if (Math.abs(change) > 0.05) { // 只记录显著变化
          bottleneckChanges.changed.push({
            type: baseBottleneck.type,
            baseSeverity: baseBottleneck.severity,
            compareSeverity: compareBottleneck.severity,
            change,
          });
        }
      }
    });

    // 计算总体评分变化
    const baseScore = baseSnapshot.report.overallScore;
    const compareScore = compareSnapshot.report.overallScore;
    const scoreAbsoluteChange = compareScore - baseScore;
    const scorePercentChange = baseScore !== 0 ? (scoreAbsoluteChange / baseScore) * 100 : 0;

    return {
      baseSnapshot,
      compareSnapshot,
      metricChanges,
      bottleneckChanges,
      scoreChange: {
        baseScore,
        compareScore,
        absoluteChange: scoreAbsoluteChange,
        percentChange: scorePercentChange,
      },
    };
  }
}
